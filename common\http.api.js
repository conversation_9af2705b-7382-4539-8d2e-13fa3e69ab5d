/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 */
// 此处第二个参数vm，就是我们在页面使用的this，你可以通过vm获取vuex等操作
const install = (Vue, vm) => {
	
	// 参数配置对象
	const config = vm.vuex_config;
	const timeout = 3000;
	
	// 将各个定义的接口名称，统一放进对象挂载到vm.$u.api(因为vm就是this，也即this.$u.api)下
	vm.$u.api = {
		
		// 基础服务：登录登出、身份信息、菜单授权、切换系统、字典数据等
		lang: (params = {}) => vm.$u.get('/lang/'+params.lang, {}, {timeout}),
		index: (params = {}) => vm.$u.get(config.adminPath+'/index', params, {}, {timeout}),
		login: (params = {}) => vm.$u.post(config.adminPath+'/login', params, {}, {timeout}),
		logout: (params = {}) => vm.$u.get(config.adminPath+'/logout', params, {}, {timeout}),
		authInfo: (params = {}) => vm.$u.get(config.adminPath+'/authInfo', params),
		menuTree: (params = {}) => vm.$u.get(config.adminPath+'/menuTree', params),
		switchSys: (params = {}) => vm.$u.get(config.adminPath+'/switch/'+params.sysCode),
		dictData: (params = {}) => vm.$u.get(config.adminPath+'/sys/dictData/treeData', params),
		dictListData: (params = {}) => vm.$u.get(config.adminPath+'/sys/dictData/listData', params),
		
		// 账号服务：验证码接口、忘记密码接口、注册账号接口等
		validCode: (params = {}) => vm.$u.getText('/validCode', params),
		getFpValidCode: (params = {}) => vm.$u.post('/account/getFpValidCode', params),
		savePwdByValidCode: (params = {}) => vm.$u.post('/account/savePwdByValidCode', params),
		getRegValidCode: (params = {}) => vm.$u.post('/account/getRegValidCode', params),
		getModiyPwdValidCode: (params = {}) => vm.$u.post('/account/getModiyPwdValidCode', params),
		saveRegByValidCode: (params = {}) => vm.$u.post('/account/saveRegByValidCode', params),
		
		// APP公共服务
		upgradeCheck: () => vm.$u.post('/app/upgrade/check', {appCode: config.appCode, appVersion: config.appVersion}),
		commentSave: (params = {}) => vm.$u.post('/app/comment/save', params),
		
		// 个人信息修改
		user: {
			infoSaveBase: (params = {}) => vm.$u.post(config.adminPath+'/sys/user/infoSaveBase', params),
			infoSavePwd: (params = {}) => vm.$u.post(config.adminPath+'/sys/user/infoSavePwd', params),
			infoSavePqa: (params = {}) => vm.$u.post(config.adminPath+'/sys/user/infoSavePqa', params),
		},
		
		// 员工用户查询
		empUser: {
			listData: (params = {}) => vm.$u.get(config.adminPath+'/sys/empUser/listData', params),
		},
		
		// 组织机构查询
		office: {
			treeData: (params = {}) => vm.$u.get(config.adminPath+'/sys/office/treeData', params),
		},
		
		// 增删改查例子
		testData: {
			form: (params = {}) => vm.$u.post(config.adminPath+'/test/testData/form', params),
			list: (params = {}) => vm.$u.post(config.adminPath+'/test/testData/listData', params),
			save: (params = {}) => vm.$u.postJson(config.adminPath+'/test/testData/save', params),
			disable: (params = {}) => vm.$u.post(config.adminPath+'/test/testData/disable', params),
			enable: (params = {}) => vm.$u.post(config.adminPath+'/test/testData/enable', params),
			delete: (params = {}) => vm.$u.post(config.adminPath+'/test/testData/delete', params),
		},
		car:{
			carddYcddrwlistData: (params = {}) => vm.$u.postJson(config.adminPath+'/cardd/ddrw/carddYcddrw/listData', params),
			carddYcddrwlform: (params = {}) => vm.$u.post(config.adminPath+'/cardd/ddrw/carddYcddrw/form', params),
			carddYcddrwlsave: (params = {}) => vm.$u.postJson(config.adminPath+'/cardd/ddrw/carddYcddrw/save', params),
			carddYcddrwlsaveBt: (params = {}) => vm.$u.postJson(config.adminPath+'/cardd/ddrw/carddYcddrw/saveBt', params),
			carddYcddrwldeleteBt: (params = {}) => vm.$u.post(config.adminPath+'/cardd/ddrw/carddYcddrw/deleteBt', params),
			
			basCarlistData: (params = {}) => vm.$u.post(config.adminPath+'/bas/car/basCar/listData', params),
			orderlistData: (params = {}) => vm.$u.post(config.adminPath+'/cardd/order/carddYcdd/listData', params),
			orderform: (params = {}) => vm.$u.post(config.adminPath+'/cardd/order/carddYcdd/form', params),
			
			coreKhdatreeData: (params = {}) => vm.$u.post(config.adminPath+'/bas/cus/coreKhda/treeData', params),
			coreZxdatreeData: (params = {}) => vm.$u.post(config.adminPath+'/cardd/zxda/coreZxda/treeData', params),
			carddYcddrwlcancel: (params = {}) => vm.$u.post(config.adminPath+'/cardd/ddrw/carddYcddrw/cancel', params),
			carddYcddrwlback: (params = {}) => vm.$u.post(config.adminPath+'/cardd/ddrw/carddYcddrw/back', params),
			basCarform: (params = {}) => vm.$u.post(config.adminPath+'/bas/car/basCar/form', params),
			coreClwzjlListData: (params = {}) => vm.$u.post(config.adminPath+'/cardd/wzjl/coreClwzjl/listDataApp', params),
			coreClwzjlForm: (params = {}) => vm.$u.post(config.adminPath+'/cardd/wzjl/coreClwzjl/form', params),
			updateClStatus: (params = {}) => vm.$u.postJson(config.adminPath+'/cardd/wzjl/coreClwzjl/updateClStatus', params),
			coreClwzjlCommit: (params = {}) => vm.$u.postJson(config.adminPath+'/cardd/wzjl/coreClwzjl/commit', params),
			carddYcddrwListCount: (params = {}) => vm.$u.post(config.adminPath+'/cardd/ddrw/carddYcddrw/listCount', params),
			carddYfgzAppData: (params = {}) => vm.$u.post(config.adminPath+'/cardd/yfgz/carddYfgz/appData', params),
			
		},
		
		// 预约收货
		yysh: {
			// 授权绑定
			// bindWxOpenid: (params = {}) => vm.$u.post(config.adminPath+'/bindWxOpenid', params),
			bindWxOpenid: (params = {}) => vm.$u.post('/weixinMa/bindWxOpenid', params),
			// 解绑
			cleanWxOpenid: (params = {}) => vm.$u.post('/weixinMa/cleanWxOpenid', params),
			
			// 获取openId
			getOpenId: (params = {}) => vm.$u.post('/weixinMa/getOpenId', params),
			
			// 发起预约
			yfPreRecordSave: (params = {}) => vm.$u.post(config.adminPath+'/bus/pre/yfPreRecord/save', params),
			// 查询预约状态
			getIsPre: (params = {}) => vm.$u.post(config.adminPath+'/bus/pre/yfPreRecord/getIsPre', params),
			// 取消预约
			yfPreRecordDelete: (params = {}) => vm.$u.post(config.adminPath+'/bus/pre/yfPreRecord/delete', params),
			// 预约记录
			yfPreRecordListData: (params = {}) => vm.$u.post(config.adminPath+'/bus/pre/yfPreRecord/listData', params),
			// 查询医院
			m8ViewHospTreeData: (params = {}) => vm.$u.post(config.adminPath+'/bas/hosp/m8ViewHosp/treeData', params),
			
			m8ViewCarTreeData: (params = {}) => vm.$u.post(config.adminPath+'/bas/car/m8ViewCar/treeData', params),
			m8ViewHospTypeTreeData: (params = {}) => vm.$u.post(config.adminPath+'/bas/hosp/m8ViewHosp/typeTreeData', params),
			
			
			// 取货记录 驾驶员
			findJsyQhData: (params = {}) => vm.$u.post(config.adminPath+'/bus/sh/yfShRecord/findJsyQhData', params),
			// 待取货清单 医院
			findNewRecord: (params = {}) => vm.$u.post(config.adminPath+'/bus/sh/yfShRecord/findNewRecord', params),
			// 取货登记
			yfShRecordSave: (params = {}) => vm.$u.postJson(config.adminPath+'/bus/sh/yfShRecord/save', params),
			
			// 台账
			hospListData: (params = {}) => vm.$u.post(config.adminPath+'/bus/extd/yfYlfw/hospListData', params),
			// 确定取货
			confirm: (params = {}) => vm.$u.post(config.adminPath+'/bus/sh/yfShRecord/confirm', params),
			
			// 待取货清单 驾驶员
			listDriverData: (params = {}) => vm.$u.post(config.adminPath+'/bus/pre/yfPreRecord/listDriverData', params),
			
			
			// 取货失败
			yfCancelRecord: (params = {}) => vm.$u.postJson(config.adminPath+'/bus/cancel/yfCancelRecord/save', params),
			
			// 医废合同
			m8ViewConstractListData: (params = {}) => vm.$u.post(config.adminPath+'/bus/ht/m8ViewConstract/listData', params),
			
			// 用户注册统计
			m8ViewHospListUserData: (params = {}) => vm.$u.post(config.adminPath+'/bas/hosp/m8ViewHosp/listUserData', params),	
			
			// 收货人员树
			empUserTreeData: (params = {}) => vm.$u.post(config.adminPath+'/sys/empUser/treeData', params),	
			
			// 获取当天的预约记录
			getPreByHosp: (params = {}) => vm.$u.post(config.adminPath+'/bus/pre/yfPreRecord/getPreByHosp', params),	
		},

	};
	
}

export default {
	install
}