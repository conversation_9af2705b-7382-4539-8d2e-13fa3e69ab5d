<template>
	<view style="display: flex;height: 100vh;flex-direction: column;">
		<!-- <view style="background-color: #fff;margin-bottom: 10px;padding: 10px;display: flex;align-items: center;">
			<view style="flex: 1;margin-right: 10px;display: flex;align-items: center;">
				<u-input style="width: 100%;"   v-model="query.name" placeholder="请输入医疗机构" :border="true" />
				
			</view>
				<u-button  type="primary" @click="loadData">
					查询
				</u-button>
		</view> -->
		
		<view style="background-color: #fff;margin-bottom: 10px;">
			<view class="search">
				<u-search @click="show = true" :show-action="false" placeholder="搜索" :disabled="true"></u-search>
			</view>
			
		</view>
		
		<view>
			<u-popup v-model="show" mode="top" length="70%">
				<view class="text-blue padding-left-sm padding-top-sm" style="font-size: 18px;padding: 10px 10px 0px;">
					<u-icon name="/static/common/img/find.png" size="64"></u-icon>
					<text class="padding-left-sm text-blue" >查询条件</text>
				</view>
				<u-form v-if="show" class="form" :model="query" ref="uForm" label-position="left">
					
					<u-form-item class="text-bold" label="医疗机构:" prop="name" label-width="200">
						<u-input    v-model="query.name" placeholder="请输入" type="text" maxlength="200"/>
						<!-- <js-select   :childName="'None'" :showFilter="true" v-model="query.code" :items="selectList"
							placeholder="请选择" :tree="false" :label-value="query.name" :flag="true"
							@label-input="query.name = $event" >
						</js-select> -->
					</u-form-item>
					
					<!-- <u-form-item class="text-bold" label="医疗机构性质:" prop="ctype" label-width="200">
						<js-select   :childName="'None'" :showFilter="true" v-model="query.ctype" :items="selectList"
							placeholder="请选择" :tree="false" :label-value="query.ctype" :flag="true"
							@label-input="query.ctype = $event" >
						</js-select>
					</u-form-item> -->
					
					<u-form-item class="text-bold" label="是否完善注册:" prop="isReg" label-width="200">
						<js-select v-model="query.isReg" dict-type="sys_yes_no" placeholder="请选择"></js-select>
					</u-form-item>
					
					<view style="display: flex;flex-wrap: wrap;margin-top: 10rpx;">
						<view class="button" v-for="(item,index) in selectList" :key="index" @click="getStateValue(item.name)">{{ item.name }}</view>
					</view>
				</u-form>
				<view style="height: 100px;">
				</view>
				<view class="footer">
					<view  style="margin-bottom: 10x;">
						<u-button class="btn" type="primary" @click="submit" >查询</u-button>
					</view>
					<u-button class="btn margin-top-lg" @click="reset">重置</u-button>
				</view>
			</u-popup>
			<!-- <u-calendar v-model="startTime" mode="range" @change="startConfirm" max-date="9999"></u-calendar> -->
		</view>
		
		<next-table class="next-table" style="flex: 1;" :show-header="true" :columns="column" :stripe="true" :fit="false" :show-summary="false" sum-text="合计"
			:summary-method="getSummaries"  :border="true"  @pageChange="pageChange"
			:data="datalist" :showPaging="true" :pageIndex="query.pageNo" :pageTotal="pageTotal">
			</next-table>
	</view>
</template>
<script>
	import util from '@/common/fire.js'
	export default {
		options: {
					styleIsolation: 'shared'
				},
		data () {
		        return {
					show: false,
					query: {
						pageNo: 1,
						pageSize: 10,
						name:''
						
					},
					formDate:'',
					showDate: false,
		            // pageIndex: 1,
		            pageTotal: 5,
		            datalist: [],
					selectList: [],
		            checkNameList: [],
		            column: [
		                // { type:'selection', fixed:true,width:60 },
		                // { name: 'name', label: '日期',fixed:false,width:80,emptyString:'--' },
		                // { name: 'age', label: '年纪',sorter:false,align:'right', },
		                // { name: 'sex', label: '性别',filters:{'0':'男','1':'女'}},
		                // { name: 'img', label: '图片',type:"img" },
		                // { name: 'address', label: '地址' },
						{ name: 'xuhao', label: '序号', fixed:true,width:60 ,align:'center',},
						{ name: 'name', label: '医疗机构', width:400,},
		                { name: 'userName', label: '用户名称', width:80,},
		                { name: 'userMobile', label: '电话号码', width:120,},
						//  { name: 'ctype', label: '医疗机构性质' , width:100,},
						// { name: 'validData', label: '生效日期' , width:100,},
						// { name: 'stopData', label: '终止日期' , width:100,},
						// { name: 'syDays', label: '将要到期天数', width:100, },
						// { name: 'isReg', label: '完善注册', width:70,filters:{'0':'否','1':'是'} },
		                
		            ]
		        }
		    },
			onShow() {
				this.getLTreeData()
			},
		    methods: {
				getLTreeData() {
					this.$u.api.yysh.m8ViewHospTypeTreeData({isShowCode: false}).then(res => {
						this.selectList = res.map(item => {
								item.value = item.id
								return item
							})
					})
				},
				getStateValue(item){
				  this.query.ctype = item;
				},
				submit() {
					setTimeout(() => {
						this.query.pageNo = 1;
						this.loadData();
					}, 100);
				},
				reset() {
					this.query = {
						pageNo: 1,
						pageSize: 10,
					};
					this.loadData();
				},
				dateChange(e) {
					console.log(e);
					// this.form.cPsDate = e.startDate + ' 至 ' + e.endDate ;
					this.formDate = e.startDate + ' 至 ' + e.endDate ;
					this.query.ddate_gte = e.startDate
					this.query.ddate_lte  = e.endDate
				},
				loadData(){
					this.show = false;
					this.query.pageNo = 1
					this.getdatalist()
				},
		        getdatalist() {
					this.$u.api.yysh.m8ViewHospListUserData(this.query).then((res) => {
						if(!res.list.length){
							this.query.pageNo = 0
						}
						this.datalist = res.list.map((item,index)=>{
							item.xuhao = index+1;
							item.userName = item.user?item.user.userName:''
							item.userMobile = item.user?item.user.mobile:''
							// item.weightGgr = util.toFixed2(item.weightGgr);
							// item.weightPs = util.toFixed2(item.weightPs);
							// item.weightSum = util.toFixed2(item.weightSum);
							return item
						})
						this.pageTotal = Math.ceil(res.count /this.query.pageSize); 
					});
		        },
		        pageChange(index) {
		            this.query.pageNo = index
		            this.getdatalist()
		        },
		    },
		    created() {
		        this.getdatalist()
		    }
	}
</script>
<style lang="scss">
	page {
		// background-color: #f8f8f8;
		background-color: #e6e6e6;
	}
	
	.button {
	  font-size: 32rpx;
	  color: #666666;
	  line-height: 40rpx;
	  padding: 12rpx 40rpx;
	  margin-bottom: 20rpx;
	  margin-right: 10rpx;
	  background: #f7f7f7;
	  border-radius: 180rpx;
	}
	.button:hover {
	  background: #3e97b0;
	  color: #ffffff;
	}

	.uni-group {
		display: flex;
		align-items: center;
	}
	.lable-text {
		text-align: justify;
	}
	
	.u-form-item {
		font-size: 28rpx !important;
		padding: 2px !important;
	}
	.footer {
		position: fixed;
		left: 0;
		bottom: 20px;
		width: 100%;
	}
	

	/deep/.table-empty.data-v-3eadd49d {
		// background: #fff;
	    // height: 63vh !important;
		height: 100% !important;;
		border:none !important;;
	}
	
	/deep/.next-table>.data-v-68ab1c7c{
		height:  calc(100% - 50px);
		background: #fff;
		// overflow-y: hidden;
	}
	
	/deep/.next-table-scroll{
		height: 100%;
		display: flex;
		flex-direction: column;
	}
	/deep/.next-table-fixed{
		flex: 1;
		// overflow: scroll;
	}
	/deep/table-empty{
		flex: 1;
	}
	
</style>