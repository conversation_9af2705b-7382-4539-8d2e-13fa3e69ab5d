<template>
	<view class="wrap">
		<js-error mode="bottom" ref="jsError"></js-error>
	
		<u-form class="form bg-white" :model="model"  ref="uForm" label-position="left" style="margin-top: -20px;">
			<view style="display: flex;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="车牌号码:" prop="clwzjlCph" label-width="150" >
					<u-input placeholder=" " v-model="model.clwzjlCph" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="品牌型号:" prop="clwzjlPpxh" label-width="150" >
					<u-input placeholder=" " v-model="model.clwzjlPpxh" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
			</view>
			
			<view style="display: flex;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="违章日期:" prop="clwzjlWzrq" label-width="150" >
					<u-input placeholder=" " v-model="model.clwzjlWzrq" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="驾驶员:" prop="clwzjlJsy" label-width="150" >
					<u-input placeholder=" " v-model="model.clwzjlJsy" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
			</view>
			
			<view style="display: flex;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="罚款金额:" prop="clwzjlFkje" label-width="150" >
					<u-input placeholder=" " v-model="model.clwzjlFkje" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="扣分值:" prop="clwzjlKfz" label-width="150" >
					<u-input placeholder=" " v-model="model.clwzjlKfz" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
			</view>
			<u-form-item  label="违章地点:" prop="clwzjlWzdd" label-width="150" >
				<u-input placeholder=" " v-model="model.clwzjlWzdd" type="text" :disabled="true" di maxlength="64"></u-input>
			</u-form-item>
			<u-form-item  label="备注:" prop="clwzjlWzdd" label-width="150" >
				{{model.remarks || ''}}
				<!-- <u-input placeholder=" " v-model="model.clwzjlWzdd" type="text" :disabled="true" di maxlength="64"></u-input> -->
			</u-form-item>
			<u-form-item style="flex: 1;" label="附件:" prop="dataMap" label-width="150" >
				<js-uploadfile uploadType="all"  v-model="model.dataMap"  imageMaxWidth="-1" imageMaxHeight="-1" :biz-key="model.id" biz-type="coreClwzjl_file"></js-uploadfile>
			</u-form-item>
		</u-form>
		
		<view style="height: 130rpx;"></view>
		<view class="footer" style="padding: 20rpx 40rpx;">
			<view style=" display: flex;justify-content: space-between; ">
				<u-button  style="flex: 1;" class="btn" type="primary" @click="handleWzcl">提交</u-button>
			</view>
		</view>
	
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// value: 
				model:{
					// id:'',
					// carNo:'',
					// planDate:'',
					// cdriver:'',
					// driverPhone:'',
					// carVenCode:'',
					// carVenName:'',
					// remarks:'',
				},
				carVenSelectList: [],
				ycddrwDyrqTime:false,
				params: {
					year: true,
					month: true,
					day: true,
					// hour: true,
					// minute: true,
					// second: true
				},
				children:[],
				pickerTime: false,//控制日期显示
			}
		},
		onLoad(params){
			// this.model = JSON.parse(params.model)
			// this.model = params
			this.$u.api.car.coreClwzjlForm({id:params.id}).then(res => {
				this.model = res.coreClwzjl
			});
			// this.$u.api.car.carddYcddrwlform({id:params.id}).then(res => {
			// 	this.model = res.carddYcddrw
				
			// 	this.children = res.carddYcddrw.carddYcddrwBtList
			// });
			
			// uni.$on('detailsData', (data)=>{
			// 	console.log(data);
			// 	if(data.index || data.index==0){
			// 		this.children[data.index] = data
			// 		this.$forceUpdate()
			// 	}else{
			// 		this.children.push(data)
			// 	}
				
			// });
			
		},
		onShow() {
			// this.children =  []
			// this.$u.api.car.coreClwzjlForm({id:this.model.id}).then(res => {
			// 	this.model = res.coreClwzjl
			// });
		},
		onReady() {
			// // 运输单位数据
			// this.$u.api.carVen.treeData().then(res => {
			// 	this.carVenSelectList = res;
			// });
		},
		methods: {
			delDetail(item, index) {
				let that = this
				uni.showModal({
					title: '删除提示',
					content: '是否删除此货位信息？',
					confirmColor: '#F54E40',
					success: (res) => {
						if (!res.confirm) {
							return false;
						}
			
						// that.children.splice(index, 1);
						// item.status = '1'
						this.$u.api.car.carddYcddrwldeleteBt({id:item.id}).then(res => {
							if(res.result == 'true'){
								this.$u.toast(res.message);
								this.children.splice(index, 1);
							}else{
								this.$refs.jsError.showError('',res.message,'error');
							}
						})
						that.$forceUpdate()
					}
				})
			},
			handleWzcl(){
				this.$u.api.car.coreClwzjlCommit({id:this.model.id,clwzjlClzt:'1',dataMap:this.model.dataMap}).then(res => {
					if(res.result == 'true'){
						this.$u.toast(res.message);
						setTimeout(()=>{
							uni.navigateBack({
								delta: 1
							})
						},500)
					}else{
						this.$refs.jsError.showError('',res.message,'error');
					}
				})
			},
			// handleWzcl(){
			// 	this.$u.api.car.updateClStatus({selIds:this.model.id,clwzjlClzt:'1',dataMap:this.model.dataMap}).then(res => {
			// 		if(res.result == 'true'){
			// 			this.$u.toast(res.message);
			// 			setTimeout(()=>{
			// 				uni.navigateBack({
			// 					delta: 1
			// 				})
			// 			},500)
			// 		}else{
			// 			this.$refs.jsError.showError('',res.message,'error');
			// 		}
			// 	})
			// },
			
			ycddrwDyrqConfirm(e) {
				//+ " " + e.hour+ ":" + e.minute + ":" + e.second
				this.model.ycddrwDyrq = e.year + "-" + e.month + "-" + e.day 
				console.log(this.model,'44455566');
			},
			edit(item,index){
				// item.index = index
				// item.['parentId.id'] = this.model.id
				let obj = {
					...item,
					'parentId.id':this.model.id
				}
				uni.navigateTo({
					url: "/pages/index/detailsForm2?item=" + JSON.stringify(obj),
				});
			},
			GoaddChild(){
				let obj = {
					parentId:{
						id:this.model.id,
					}
				}
				uni.navigateTo({
					url: "/pages/index/detailsForm2?item=" + JSON.stringify(obj),
				});
			},
			renwu(){},
			chejian(){
				// uni.navigateTo({
				// 	url: "/pages/index/form2?id=" + this.model.id,
				// });
			},
			baocun(){},
			quxiao(){
				uni.navigateBack({
					delta: 1
				})
			},
			showKeyboard(ref){
				this.$refs[ref].toShow(this.model.carNo)
			},
			timeConfirm(e){
				this.model.planDate = e.year + '-' + e.month + '-' + e.day +' '+e.hour +":"+e.minute +":"+e.second;
			},
			submit(data, callback) {
				if(this.model.carNo == null || this.model.carNo == ''){
					this.$refs.jsError.showError('','请正确填入车牌号！','error');
					return;
				}else if(this.model.planDate == null || this.model.planDate == ''){
					this.$refs.jsError.showError('','请正确选择计划发货日期！','error');
					return;
				}else if(this.model.carVenCode == null || this.model.carVenCode == ''){
					this.$refs.jsError.showError('','请正确选择运输单位！','error');
					return;
				}else if(this.model.ipicture == undefined || this.model.ipicture == ''){
					this.$refs.jsError.showError('','请必填图片是否可再次上传！','error');
					return;
				}else{
					var data = {
						mfCarplanFhH: JSON.stringify(this.model),
						useStatus: 1
					};
					this.$u.api.mffh.save(data).then(res => {
						if(res.result == 'true'){
							this.$u.toast(res.message);
							setTimeout(()=>{
								uni.$emit('refreshData');
								uni.navigateBack({
									delta: 1
								})
							},500)
						}else{
							this.$refs.jsError.showError('',res.message,'error');
						}
					});
				}
			},
		}
	}
</script>
<style scoped  lang="less">
	
.footer {
	position: fixed;
	left: 0;
	bottom: 0;
	width: 100%;
	background: #fff;z-index: 999;
	border-top: 1px solid #aaa;
}

.cu-bar {
		min-height: 60px;
	}

	.cu-btn {
		font-size: 16px;
		height: 40px;
	}

	.foot {
		background: #fff;
	}

	.cu-item {
		flex-wrap: wrap;
		padding: 8px 15px !important;
	}

	.title {
		width: 220rpx;
	}

	.cu-modal-slot {
		margin: 42rpx 32rpx 20rpx 32rpx;
		height: 100vh;
		position: relative;
	}

	.cu-modal-header {
		font-size: 38rpx;
		color: #3d3d3d;
		line-height: 40rpx;
		text-align: left;
		font-weight: 500;
	}

	.button {
		font-size: 32rpx;
		color: #666666;
		line-height: 40rpx;
		padding: 12rpx 40rpx;
		margin-bottom: 20rpx;
		background: #e2e2e2;
		border-radius: 180rpx;
	}

	.button:hover {
		background: #3e97b0;
		color: #ffffff;
	}

	.cu-modal-search-title {
		margin: 32rpx 0rpx 20rpx 0rpx;
		text-align: left;
		font-size: 32rpx;
		color: #999999;
		line-height: 40rpx;
	}

	.cu-dialog {
		background: #ffffff;
	}

	.cu-modal-footer {
		padding: 32rpx 32rpx;
		width: 100%;

		.cu-btn {
			width: 50%;
		}
	}

	.bg-confirm {
		background: #3e97b0;
		color: #ffffff;
	}
</style>