<template>
	<view class="wrap">
		<js-error mode="bottom" ref="jsError"></js-error>
		<!-- <view class="cu-bar bg-white ">
			<view class="action">
				<u-icon name="/static/image/invcode.png" size="80"></u-icon>
				<text class="cuIcon-titles text-bold">发货信息</text> 
			</view>
		</view> -->
		<u-form class="form bg-white" :model="model"  ref="uForm" label-position="left" style="margin-top: -20px;">
			<u-form-item  label="任务单号:" prop="ycddrwRwdh" label-width="150" >
				<u-input placeholder=" " v-model="model.ycddrwRwdh" type="text" :disabled="true" di maxlength="64"></u-input>
			</u-form-item>
			<u-form-item label="订单编号:" prop="ycddDdbh" label-width="150" >
				<u-input placeholder=" " v-model="model.carddYcdd.ycddDdbh" type="text" :disabled="true" di maxlength="64"></u-input>
			</u-form-item>
			<!-- <view style="display: flex;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="任务单号:" prop="ycddrwRwdh" label-width="150" >
					<u-input placeholder=" " v-model="model.ycddrwRwdh" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="订单编号:" prop="ycddDdbh" label-width="150" >
					<u-input placeholder=" " v-model="model.carddYcdd.ycddDdbh" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
			</view> -->
			<u-form-item label="用车单位:" prop="khdaDwqc" label-width="150" >
				<u-input placeholder=" " v-model="model.carddYcdd.basCus.khdaDwqc" type="text" :disabled="true" di maxlength="64"></u-input>
			</u-form-item>
			<u-form-item label="部门:" prop="ycddBm" label-width="150" >
				<u-input placeholder=" " v-model="model.carddYcdd.ycddBm" type="text" :disabled="true" di maxlength="64"></u-input>
			</u-form-item>
			
			<view style="display: flex;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="联系人:" prop="khlxrLxrmc" label-width="150" >
					<u-input placeholder=" " v-model="model.carddYcdd.contact.khlxrLxrmc" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="联系电话:" prop="carNo" label-width="150" >
					<u-input @click="callNumber(model.carddYcdd.ycddLxdh)" placeholder=" " v-model="model.carddYcdd.ycddLxdh" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
			</view>
			<view style="display: flex;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="租赁方式:" prop="btZlfs" label-width="150" >
					<js-select v-model="model.carddYcddBt.btZlfs" :disabled="true" dict-type="car_zlfs" placeholder=" "></js-select>
				</u-form-item>
				<!-- <u-form-item style="flex: 1;"  label="业务员:" prop="ycddYwy" label-width="150" >
					<u-input placeholder=" " v-model="model.carddYcdd.ycddYwy" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item> -->
				<u-form-item style="flex: 1;" label="品牌型号:" prop="btPpxh" label-width="150" >
					<js-select v-model="model.carddYcddBt.btPpxh" :disabled="true" dict-type="car_type" placeholder=" "></js-select>
				</u-form-item>
			</view>
			<!-- <view style="display: flex;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="品牌型号:" prop="btPpxh" label-width="150" >
					<js-select v-model="model.carddYcddBt.btPpxh" :disabled="true" dict-type="car_type" placeholder=" "></js-select>
				</u-form-item>
				<u-form-item  style="flex: 1;" label="车单编号:" prop="ycddrwCdbh" label-width="150" >
					<u-input :placeholder="model.ycddrwRwzt=='6'?'请输入':' ' " v-model="model.ycddrwCdbh" type="text" :disabled="model.ycddrwRwzt=='6'?false:true" di maxlength="64"></u-input>
				</u-form-item>
			</view> -->
			<view style="display: flex;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="车牌号:" prop="btCph" label-width="150" >
					<u-input placeholder=" " v-model="model.carddYcddBt.btCph" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="驾驶员:" prop="btJsy" label-width="150" >
					<u-input placeholder=" " v-model="model.carddYcddBt.btJsy" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
			</view>
			
			<view style="display: flex;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="上车地点:" prop="btScdd" label-width="150" >
					<u-input placeholder=" " v-model="model.carddYcddBt.btScdd" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="上车时间:" prop="btScsj" label-width="150" >
					<u-input placeholder=" " v-model="model.carddYcddBt.btScsj" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
			</view>
			<u-form-item   label="车单编号:" prop="ycddrwCdbh" label-width="150" >
				<u-input placeholder=" " v-model="model.ycddrwCdbh" type="text" :disabled="true" di maxlength="64"></u-input>
			</u-form-item>
			<u-form-item  label="车单照片:" prop="ycddrwCdbh" label-width="150" >
				<js-uploadfile maxCount="0"  v-model="model.dataMap12"  imageMaxWidth="-1" imageMaxHeight="-1" :biz-key="model.id" biz-type="carddYcddrw_file_cdzp" ></js-uploadfile>
			</u-form-item>
			
			<!-- <view style="display: flex;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="开始日期:" prop="btKsrq" label-width="150" >
					<u-input placeholder=" " v-model="model.carddYcddBt.btKsrq" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="结束日期:" prop="btJsrq" label-width="150" >
					<u-input placeholder=" " v-model="model.carddYcddBt.btJsrq" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
			</view> -->
			<u-form-item  label="车辆行程:" prop="btScdd" label-width="150" >
				<u-input placeholder=" " v-model="model.carddYcdd.ycddClxc" type="text" :disabled="true" di maxlength="64"></u-input>
			</u-form-item>
			<u-form-item  label="车辆要求:" prop="btScsj" label-width="150" >
				<u-input placeholder=" " v-model="model.carddYcdd.ycddClyq" type="text" :disabled="true" di maxlength="64"></u-input>
			</u-form-item>
			<u-form-item  label="用车日期:" prop="btKsrq" label-width="150" >
				<view class="">
					{{model.carddYcddBt?model.carddYcddBt.btKsrq:''}} - {{model.carddYcddBt?model.carddYcddBt.btJsrq:''}}
				</view>
			</u-form-item>
			<!-- <u-form-item label="车辆行程及要求:" prop="ycddrwCdbh" label-width="230" >
				<u-input placeholder=" " v-model="model.ycddrwCdbh" type="text" :disabled="true" di maxlength="64"></u-input>
			</u-form-item> -->
		
			<u-form-item label="拍照时间:" prop="ycddrwPzsj" label-width="150" >
				<u-input placeholder=" " v-model="model.ycddrwPzsj" type="text" :disabled="true" di maxlength="64"></u-input>
			</u-form-item>
			
			<u-form-item label="出发前照片" prop="images" label-position="top" required >
				<js-uploadfile  v-model="model.dataMap" :sourceType="['camera']" imageMaxWidth="-1" imageMaxHeight="-1" :biz-key="model.id" biz-type="carddYcddrw_image_cf"></js-uploadfile>
			</u-form-item>
			<u-form-item label="车辆前方" prop="images" label-position="top" required >
				<js-uploadfile v-model="model.dataMap1" :sourceType="['camera']" imageMaxWidth="-1" imageMaxHeight="-1" :biz-key="model.id" biz-type="carddYcddrw_image_qian"></js-uploadfile>
			</u-form-item>
			<u-form-item label="车辆右侧" prop="images" label-position="top" required >
				<js-uploadfile v-model="model.dataMap2" :sourceType="['camera']" imageMaxWidth="-1" imageMaxHeight="-1" :biz-key="model.id" biz-type="carddYcddrw_image_you"></js-uploadfile>
			</u-form-item>
			<u-form-item label="车辆左侧" prop="images" label-position="top" required >
				<js-uploadfile v-model="model.dataMap3" :sourceType="['camera']" imageMaxWidth="-1" imageMaxHeight="-1" :biz-key="model.id" biz-type="carddYcddrw_image_zuo"></js-uploadfile>
			</u-form-item>
			<u-form-item label="车辆后方" prop="images" label-position="top" required >
				<js-uploadfile v-model="model.dataMap4" :sourceType="['camera']" imageMaxWidth="-1" imageMaxHeight="-1" :biz-key="model.id" biz-type="carddYcddrw_image_hou"></js-uploadfile>
			</u-form-item>
			<!-- <u-picker mode="time" v-model="pickerTime" @confirm="timeConfirm" :params="params"></u-picker>
			<u-form-item label="司机:" prop="cdriver" label-width="230" :label-style="{'font-weight':'bold'}">
				<u-input placeholder="请输入" v-model="model.cdriver" type="text"  di maxlength="64"></u-input>
			</u-form-item>
			<u-form-item label="司机电话:" prop="driverPhone" label-width="230" :label-style="{'font-weight':'bold'}">
				<u-input placeholder="请输入" v-model="model.driverPhone" type="text"  di maxlength="64"></u-input>
			</u-form-item>
			<u-form-item label="运输单位:" prop="testCarVen" label-width="230" required :label-style="{'font-weight':'bold'}">
				<js-select v-model="model.carVenCode" :showFilter="true" :items="carVenSelectList"  placeholder="请输入" :tree="true"
					:label-value="model.carVenName" @label-input="model.carVenName = $event"></js-select>
			</u-form-item> -->
			<!-- <u-form-item label="是否再次上传:" prop="ipicture" label-width="230" required :label-style="{'font-weight':'bold'}">
				<js-radio v-model="model.ipicture" dict-type="sys_yes_no"></js-radio>
			</u-form-item> 
			<u-form-item label="备注:" prop="remarks" label-width="230" :label-style="{'font-weight':'bold'}">
				<u-input placeholder="请输入" v-model="model.remarks" type="text"  di maxlength="64"></u-input>
			</u-form-item>
			<u-form-item label="上传图片（选填）" prop="images" label-position="top" :label-style="{'font-weight':'bold'}">
				<js-uploadfile v-model="model.dataMap" imageMaxWidth="-1" imageMaxHeight="-1" :biz-key="model.id" biz-type="carplanFhH_image"></js-uploadfile>
			</u-form-item> -->
		</u-form>
		<view style="height: 250rpx;"></view>
		<!-- <view class="form-footer b-btns">
			<view class="footer">
				<u-button class="btn" type="primary" @click="submit">保存</u-button>
				<u-button class="btn" type="primary" @click="submit">保存</u-button>
				<u-button class="btn" type="primary" @click="submit">保存</u-button>
				<u-button class="btn" type="primary" @click="submit">保存</u-button>
			</view>
		</view> -->
		<view class="footer" style="padding: 20rpx 40rpx;">
			<view style=" display: flex;justify-content: space-between;margin-bottom: 20rpx; ">
				<u-button style="flex: 1;margin-right: 20rpx;" class="btn" type="primary" @click="handleqx()">取消任务</u-button>
				<u-button v-if="model.showBackBtn" style="flex: 1;margin-right: 20rpx;"  class="btn" type="primary" @click="handleBack()">退回</u-button>
				<u-button v-if="model.showRwqrBtn" style="flex: 1;margin-right: 20rpx;"  class="btn" type="primary" @click="handleSubmit('1',model.ycddrwRwzt,'2')">任务确认</u-button>
				<u-button v-if="model.showCjwcBtn" style="flex: 1;" class="btn" type="primary" @click="handleSubmit('1', model.ycddrwRwzt, '3')">车检完成</u-button>
			</view>
			<view style=" display: flex;justify-content: space-between; ">
				<u-button v-if="model.showQxqrBtn" style="flex: 1;margin-right: 20rpx;" class="btn" type="primary"  @click="handleSubmit('1', model.ycddrwRwzt, '1')">取消确认</u-button>
				<u-button v-if="model.showBcrwBtn" style="flex: 1;" class="btn" type="primary" @click="handleSubmit('1', model.ycddrwRwzt, '6')">提交任务</u-button>
			</view>
		</view>
		
		<!-- <view class="btn-group cu-bar foot">
			<button class="cu-btn bg-green shadow-blur" @tap="submit()">
				<text class="cuIcon-add"></text> 保存
			</button>
		</view> -->
		<u-modal v-model="rwshow" title='任务退回提醒' :mask-close-able="true" :show-cancel-button="true">
			<view style="padding: 20px 10px;">
					退回原因：{{model.ycddrwThyy  || ''}}
			</view>
		</u-modal>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// value:
				rwshow:false,
				flag:false,
				model:{
					// id:'',
					// carNo:'',
					// planDate:'',
					// cdriver:'',
					// driverPhone:'',
					// carVenCode:'',
					// carVenName:'',
					// remarks:'',
				},
				carVenSelectList: [],
				params: {
					year: true,
					month: true,
					day: true,
					hour: true,
					minute: true,
					second: true
				},
				pickerTime: false,//控制日期显示
			}
		},
		onLoad(params){
			// this.model = JSON.parse(params.model)
			this.$u.api.car.carddYcddrwlform({id:params.id}).then(res => {
				this.model = res.carddYcddrw
				if (this.model.status == '5') {
				      this.rwshow = true
				}
				setTimeout(()=>{
					this.flag = true
				},500)
			});
		},
		watch: {
			'model.dataMap.carddYcddrw_image_cf'(val, oldVal) {
				console.log(oldVal,'888',val,'999');
				if(this.flag){
					this.model.ycddrwPzsj = this.fmtDate()
					this.$forceUpdate()
				}
			},
		},
		onShow() {
		},
		onReady() {
			// // 运输单位数据
			// this.$u.api.carVen.treeData().then(res => {
			// 	this.carVenSelectList = res;
			// });
		},
		methods: {
			fmtDate(){
			  // 格式化年月日
			  var date = new Date();
			  var y = date.getFullYear();
			  var m = date.getMonth() + 1;
			  var d = date.getDate();
			  
			  var h = date.getHours();
			  var f = date.getMinutes();
			  var s = date.getSeconds();
			  return y + "-" + (m < 10 ? "0" + m : m) + "-" + (d < 10 ? "0" + d : d) + ' ' + (h < 10 ? "0" + h : h) + ":" +(f < 10 ? "0" + f : f)+ ":"  + (s < 10 ? "0" + s : s);
			  
				// var yyy = new Date().toLocaleDateString();
				// var time = new Date().toLocaleString()
				// return time
			},
			handleqx(){
				let params = {
					isNewRecord: this.model.isNewRecord,
					id: this.model.id,
				}
				this.$u.api.car.carddYcddrwlcancel(params).then(res => {
					if(res.result == 'true'){
						this.$u.toast(res.message);
						setTimeout(()=>{
							uni.navigateBack({
								delta: 1
							})
						},500)
					}else{
						this.$refs.jsError.showError('',res.message,'error');
					}
				})
			},
			handleBack(){
				let params = {
					isNewRecord: this.model.isNewRecord,
					id: this.model.id,
					oldStatus: this.model.ycddrwRwzt,
				}
				this.$u.api.car.carddYcddrwlback(params).then(res => {
					if(res.result == 'true'){
						this.$u.toast(res.message);
						setTimeout(()=>{
							uni.navigateBack({
								delta: 1
							})
						},500)
					}else{
						this.$refs.jsError.showError('',res.message,'error');
					}
				})
			},
			handleSubmit(ope, oldStatus, newStatus){
				console.log(this.model,'222');
				if(newStatus == '3' || newStatus == '6'){
					if (!this.model.dataMap || !this.model.dataMap.carddYcddrw_image_cf) {
						this.$u.toast("请上传出发前照片");
						return;
					}
					if (!this.model.dataMap1 || !this.model.dataMap1.carddYcddrw_image_qian) {
						this.$u.toast("请上传车辆前方图片");
						return;
					}
					if (!this.model.dataMap2 || !this.model.dataMap2.carddYcddrw_image_you) {
						this.$u.toast("请上传车辆右侧图片");
						return;
					}
					if (!this.model.dataMap3 || !this.model.dataMap3.carddYcddrw_image_zuo) {
						this.$u.toast("请上传车辆左侧图片");
						return;
					}
					if (!this.model.dataMap4 || !this.model.dataMap4.carddYcddrw_image_hou) {
						this.$u.toast("请上传车辆后方图片");
						return;
					}
				}
				let data = {
					...this.model,
					dataMap:{
						...this.model.dataMap,
						...this.model.dataMap1,
						...this.model.dataMap2,
						...this.model.dataMap3,
						...this.model.dataMap4,
					},
					ope,
					oldStatus,
					newStatus
				}
				this.$u.api.car.carddYcddrwlsave(data).then(res => {
					if(res.result == 'true'){
						this.$u.toast(res.message);
						setTimeout(()=>{
							uni.navigateBack({
								delta: 1
							})
						},500)
					}else{
						this.$refs.jsError.showError('',res.message,'error');
					}
				})
			},
			callNumber(phone) {
				let that = this
				if(phone){
					uni.makePhoneCall({
						phoneNumber: phone,
						success: () => {
							console.log('拨打电话成功！');
						},
						fail: () => {
							that.$u.toast('拨打电话失败！');
							// console.error('拨打电话失败！');
						}
					});
				}
			},
			renwu(){},
			chejian(){
				uni.navigateTo({
					url: "/pages/index/form2?id=" + this.model.id,
				});
			},
			baocun(){},
			quxiao(){
				uni.navigateBack({
					delta: 1
				})
			},
			showKeyboard(ref){
				this.$refs[ref].toShow(this.model.carNo)
			},
			timeConfirm(e){
				this.model.planDate = e.year + '-' + e.month + '-' + e.day +' '+e.hour +":"+e.minute +":"+e.second;
			},
			submit(data, callback) {
				if(this.model.carNo == null || this.model.carNo == ''){
					this.$refs.jsError.showError('','请正确填入车牌号！','error');
					return;
				}else if(this.model.planDate == null || this.model.planDate == ''){
					this.$refs.jsError.showError('','请正确选择计划发货日期！','error');
					return;
				}else if(this.model.carVenCode == null || this.model.carVenCode == ''){
					this.$refs.jsError.showError('','请正确选择运输单位！','error');
					return;
				}else if(this.model.ipicture == undefined || this.model.ipicture == ''){
					this.$refs.jsError.showError('','请必填图片是否可再次上传！','error');
					return;
				}else{
					var data = {
						mfCarplanFhH: JSON.stringify(this.model),
						useStatus: 1
					};
					this.$u.api.mffh.save(data).then(res => {
						if(res.result == 'true'){
							this.$u.toast(res.message);
							setTimeout(()=>{
								uni.$emit('refreshData');
								uni.navigateBack({
									delta: 1
								})
							},500)
						}else{
							this.$refs.jsError.showError('',res.message,'error');
						}
					});
				}
			},
		}
	}
</script>
<style scoped  lang="less">
.footer {
	position: fixed;
	left: 0;
	bottom: 0;
	width: 100%;
	background: #fff;z-index: 999;
	border-top: 1px solid #aaa;
}

.cu-bar {
		min-height: 60px;
	}

	.cu-btn {
		font-size: 16px;
		height: 40px;
	}

	.foot {
		background: #fff;
	}

	.cu-item {
		flex-wrap: wrap;
		padding: 8px 15px !important;
	}

	.title {
		width: 220rpx;
	}

	.cu-modal-slot {
		margin: 42rpx 32rpx 20rpx 32rpx;
		height: 100vh;
		position: relative;
	}

	.cu-modal-header {
		font-size: 38rpx;
		color: #3d3d3d;
		line-height: 40rpx;
		text-align: left;
		font-weight: 500;
	}

	.button {
		font-size: 32rpx;
		color: #666666;
		line-height: 40rpx;
		padding: 12rpx 40rpx;
		margin-bottom: 20rpx;
		background: #e2e2e2;
		border-radius: 180rpx;
	}

	.button:hover {
		background: #3e97b0;
		color: #ffffff;
	}

	.cu-modal-search-title {
		margin: 32rpx 0rpx 20rpx 0rpx;
		text-align: left;
		font-size: 32rpx;
		color: #999999;
		line-height: 40rpx;
	}

	.cu-dialog {
		background: #ffffff;
	}

	.cu-modal-footer {
		padding: 32rpx 32rpx;
		width: 100%;

		.cu-btn {
			width: 50%;
		}
	}

	.bg-confirm {
		background: #3e97b0;
		color: #ffffff;
	}
</style>