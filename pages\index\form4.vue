<template>
	<view class="wrap">
		<js-error mode="bottom" ref="jsError"></js-error>
		<!-- <view class="cu-bar bg-white ">
			<view class="action">
				<u-icon name="/static/image/invcode.png" size="80"></u-icon>
				<text class="cuIcon-titles text-bold">发货信息</text> 
			</view>
		</view> -->
		<u-form class="form bg-white" :model="model"  ref="uForm" label-position="left" style="margin-top: -20px;">
			<u-form-item  label="任务单号:" prop="ycddrwRwdh" label-width="150" >
				<u-input placeholder=" " v-model="model.ycddrwRwdh" type="text" :disabled="true" di maxlength="64"></u-input>
			</u-form-item>
			<u-form-item label="订单编号:" prop="ycddDdbh" label-width="150" >
				<u-input placeholder=" " v-model="model.carddYcdd.ycddDdbh" type="text" :disabled="true" di maxlength="64"></u-input>
			</u-form-item>
			<!-- <view style="display: flex;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="任务单号:" prop="ycddrwRwdh" label-width="150" >
					<u-input placeholder=" " v-model="model.ycddrwRwdh" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="订单编号:" prop="ycddDdbh" label-width="150" >
					<u-input placeholder=" " v-model="model.carddYcdd.ycddDdbh" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
			</view> -->
			<u-form-item label="用车单位:" prop="khdaDwqc" label-width="150" >
				<u-input placeholder=" " v-model="model.carddYcdd.basCus.khdaDwqc" type="text" :disabled="true" di maxlength="64"></u-input>
			</u-form-item>
			<u-form-item label="部门:" prop="ycddBm" label-width="150" >
				<u-input placeholder=" " v-model="model.carddYcdd.ycddBm" type="text" :disabled="true" di maxlength="64"></u-input>
			</u-form-item>
			
			<view style="display: flex;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="联系人:" prop="khlxrLxrmc" label-width="150" >
					<u-input placeholder=" " v-model="model.carddYcdd.contact.khlxrLxrmc" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="联系电话:" prop="carNo" label-width="150" >
					<u-input @click="callNumber(model.carddYcdd.ycddLxdh)" placeholder=" " v-model="model.carddYcdd.ycddLxdh" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
			</view>
			<view style="display: flex;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="租赁方式:" prop="btZlfs" label-width="150" >
					<js-select v-model="model.carddYcddBt.btZlfs" :disabled="true" dict-type="car_zlfs" placeholder=" "></js-select>
				</u-form-item>
				<!-- <u-form-item style="flex: 1;"  label="业务员:" prop="ycddYwy" label-width="150" >
					<u-input placeholder=" " v-model="model.carddYcdd.ycddYwy" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item> -->
				<u-form-item style="flex: 1;" label="品牌型号:" prop="btPpxh" label-width="150" >
					<js-select v-model="model.carddYcddBt.btPpxh" :disabled="true" dict-type="car_type" placeholder=" "></js-select>
				</u-form-item>
			</view>
			<!-- <view style="display: flex;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="品牌型号:" prop="btPpxh" label-width="150" >
					<js-select v-model="model.carddYcddBt.btPpxh" :disabled="true" dict-type="car_type" placeholder=" "></js-select>
				</u-form-item>
				<u-form-item  style="flex: 1;" label="车单编号:" prop="ycddrwCdbh" label-width="150" >
					<u-input placeholder=" " v-model="model.ycddrwCdbh" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
			</view> -->
			<view style="display: flex;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="车牌号:" prop="btCph" label-width="150" >
					<u-input placeholder=" " v-model="model.carddYcddBt.btCph" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="驾驶员:" prop="btJsy" label-width="150" >
					<u-input placeholder=" " v-model="model.carddYcddBt.btJsy" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
			</view>
			
			<view style="display: flex;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="上车地点:" prop="btScdd" label-width="150" >
					<u-input placeholder=" " v-model="model.carddYcddBt.btScdd" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="上车时间:" prop="btScsj" label-width="150" >
					<u-input placeholder=" " v-model="model.carddYcddBt.btScsj" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
			</view>
			
			<!-- <view style="display: flex;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="开始日期:" prop="btKsrq" label-width="150" >
					<u-input placeholder=" " v-model="model.carddYcddBt.btKsrq" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="结束日期:" prop="btJsrq" label-width="150" >
					<u-input placeholder=" " v-model="model.carddYcddBt.btJsrq" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
			</view> -->
			<u-form-item  label="车辆行程:" prop="btScdd" label-width="150" >
				<u-input placeholder=" " v-model="model.carddYcdd.ycddClxc" type="text" :disabled="true" di maxlength="64"></u-input>
			</u-form-item>
			<u-form-item  label="车辆要求:" prop="btScsj" label-width="150" >
				<u-input placeholder=" " v-model="model.carddYcdd.ycddClyq" type="text" :disabled="true" di maxlength="64"></u-input>
			</u-form-item>
			<u-form-item  label="用车日期:" prop="btKsrq" label-width="150" >
				<view class="">
					{{model.carddYcddBt?model.carddYcddBt.btKsrq:''}} - {{model.carddYcddBt?model.carddYcddBt.btJsrq:''}}
				</view>
			</u-form-item>
			<u-form-item   label="用车人:" prop="ycddrwLxr" label-width="150" >
				<u-input placeholder=" " v-model="model.ycddrwLxr" type="text" :disabled="true" di maxlength="64"></u-input>
			</u-form-item>
			<u-form-item   label="车单编号:" prop="ycddrwCdbh" label-width="150" >
				<u-input placeholder=" " v-model="model.ycddrwCdbh" type="text" :disabled="true" di maxlength="64"></u-input>
			</u-form-item>
			<u-form-item  label="车单照片:" prop="ycddrwCdbh" label-width="150" >
				<js-uploadfile maxCount="0"  v-model="model.dataMap12"  imageMaxWidth="-1" imageMaxHeight="-1" :biz-key="model.id" biz-type="carddYcddrw_file_cdzp" ></js-uploadfile>
			</u-form-item>
			<!-- <u-form-item label="车辆行程及要求:" prop="ycddrwCdbh" label-width="230" >
				<u-input placeholder=" " v-model="model.ycddrwCdbh" type="text" :disabled="true" di maxlength="64"></u-input>
			</u-form-item> -->
			
			
		
			
			
		</u-form>
		<view class="action bg-white "
			style="font-size: 20px;align-items: center;display: flex;justify-content: space-between;padding: 0 10px 10px 10px;">
		
			<view style="align-items: center;display: flex;">
				<!-- <u-icon name="/static/image/detail.png" size="80"></u-icon> -->
				<text class="cuIcon-titles text-bold">任务过程</text>
			</view>
		
		
			<!-- <view @click="GoaddChild()">
				<u-icon name="plus-circle" size="90" color="#3E97B0"></u-icon>
			</view> -->
		</view>
		<view style="padding: 10px;">
			<u-empty v-if="!children.length" text="暂无任务过程" mode="data"></u-empty>
			<view v-for="(item,index) in children" class="cu-item shadow " style="position: relative;border: 1px solid #eee;border-radius: 10rpx;margin-bottom: 10rpx;" :key="index" v-if="item.status != '1'">
				<view class="cu-form-group" style="display: flex;justify-content: space-between;">
					<view style=" display: inline-block;padding: 5px;background: #3E97B0;color: #fff;min-width: 40px;text-align: center;font-style: italic;font-weight: bold">
						{{ index + 1 }}
					</view>
					<view class="">
						<!-- <u-icon name="trash" size="65" color="red" @click.stop="delDetail(item,index)"></u-icon> -->
						
					</view>
				</view>
				<view @click="edit(item,index)" >
				<!-- <u-form-item style="flex: 1;" label="日期:" prop="btRq" label-width="230" >
					{{item.btRq || ''}}
				</u-form-item> -->
				<u-form-item style="flex: 1;" label="行驶路线:" prop="btXslx" label-width="230" >
					{{item.btXslx || ''}}
					<!-- <u-input placeholder=" " v-model="item.btXslx" type="text" :disabled="true"  di maxlength="64"></u-input> -->
				</u-form-item>
				<!-- <u-form-item style="flex: 1;" label="起始路码:" prop="btQslm" label-width="230" >
					<u-input placeholder=" " v-model="item.btQslm" type="text" :disabled="true"  di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="起始路码照片:" prop="upload" label-width="230" >
					<js-uploadfile  v-model="item.dataMap" maxCount="0"  imageMaxWidth="-1" imageMaxHeight="-1" :biz-key="item.id" biz-type="carddYcddrw_file_bt_qs"></js-uploadfile>
				</u-form-item>
				<u-form-item style="flex: 1;" label="结束路码:" prop="btJslm" label-width="230" >
					<u-input placeholder=" " v-model="item.btJslm" type="text"  :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="结束路码照片:" prop="upload" label-width="230" >
					<js-uploadfile  v-model="item.dataMap"  maxCount="0" imageMaxWidth="-1" imageMaxHeight="-1" :biz-key="item.id" biz-type="carddYcddrw_file_bt_js"></js-uploadfile>
				</u-form-item>
				<u-form-item style="flex: 1;" label="实际公里:" prop="btSjgl" :disabled="true" label-width="230" >
					<u-input placeholder=" " :disabled="true" v-model="item.btSjgl" type="text"  di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="上车时间:" prop="btScsj" label-width="230" >
					<u-input placeholder=" " v-model="item.btScsj" type="text" :disabled="true"/>
				</u-form-item>
				<u-form-item style="flex: 1;" label="下车时间:" prop="btXcsj" label-width="230" >
					<u-input placeholder=" " v-model="item.btXcsj" type="text" :disabled="true"/>
				</u-form-item>
				<u-form-item style="flex: 1;" label="超时时间（小时):" prop="btCssjxs" label-width="230" >
					<u-input placeholder=" " v-model="item.btCssjxs" type="digit" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="备注:" prop="btBz" label-width="230" >
					<u-input placeholder=" " v-model="item.btBz" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item> -->
				</view>
			</view>
		</view>
		
		
		
		
		<u-form class="form bg-white" :model="model"  ref="uForm" label-position="left" style="margin-top: -20px;">
			
			<view style="display: flex;align-items: center;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="收费公里:" prop="ycddrwSfgls" label-width="170" >
					<u-input placeholder=" " v-model="model.ycddrwSfgls" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="实际公里数:" prop="ycddrwSjgls" label-width="170" >
					<u-input placeholder=" " v-model="model.ycddrwSjgls" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
			</view>
			
			<view style="display: flex;align-items: center;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="绩效公里:" prop="ycddrwJxgls" label-width="170" >
					<u-input placeholder=" " v-model="model.ycddrwJxgls" :disabled="true" type="digit"  di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="打印日期:" prop="ycddrwDyrq" label-width="170" >
					<u-input placeholder=" " v-model="model.ycddrwDyrq" :disabled="true" />
				</u-form-item>
				<u-picker mode="time" :params="params" v-model="ycddrwDyrqTime" @confirm="ycddrwDyrqConfirm"></u-picker>
			</view>
			
			<view style="display: flex;align-items: center;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="过路费:" prop="ycddrwGlf" label-width="170" >
					<u-input placeholder=" " v-model="model.ycddrwGlf" :disabled="true" type="digit" di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="过路凭据:" prop="ycddrwSjgls" label-width="170" >
					<js-uploadfile  v-model="model.dataMap" maxCount="0" imageMaxWidth="-1" imageMaxHeight="-1" :biz-key="model.id" biz-type="carddYcddrw_file_glf"></js-uploadfile>
				</u-form-item>
			</view>
			
			<!-- <view style="display: flex;align-items: center;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="加油费:" prop="ycddrwJyf" label-width="170" >
					<u-input placeholder=" " v-model="model.ycddrwJyf" :disabled="true" type="digit"  di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="加油凭据:" prop="ycddrwSjgls" label-width="170" >
					<js-uploadfile  v-model="model.dataMap" maxCount="0"  imageMaxWidth="-1" imageMaxHeight="-1" :biz-key="model.id" biz-type="carddYcddrw_file_wgyf"></js-uploadfile>
				</u-form-item>
			</view> -->
			
			<view style="display: flex;align-items: center;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="现金加油费:" prop="ycddrwJyf" label-width="170" >
					<u-input placeholder=" " v-model="model.ycddrwJyf" :disabled="true" type="digit"  di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="现金加油凭据:" prop="ycddrwSjgls" label-width="170"  >
					<js-uploadfile uploadType="all" maxCount="0"  v-model="model.dataMap2"  imageMaxWidth="-1" imageMaxHeight="-1" :biz-key="model.id" biz-type="carddYcddrw_file_wgyf"></js-uploadfile>
				</u-form-item>
			</view>
			<view style="display: flex;align-items: center;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="油卡加油费:" prop="ycddrwYkjyf" label-width="170" >
					<u-input placeholder=" " v-model="model.ycddrwYkjyf" :disabled="true" type="digit"  di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="油卡加油凭据:" prop="ycddrwSjgls" label-width="170" >
					<js-uploadfile uploadType="all" maxCount="0" v-model="model.dataMap13"  imageMaxWidth="-1" imageMaxHeight="-1" :biz-key="model.id" biz-type="carddYcddrw_file_ykjyf"></js-uploadfile>
				</u-form-item>
			</view>
			
			
			
			<view style="display: flex;align-items: center;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="住宿费:" prop="ycddrwZsf" label-width="170" >
					<u-input placeholder=" " v-model="model.ycddrwZsf" :disabled="true" type="digit"  di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="住宿凭据:" prop="ycddrwSjgls" label-width="170" >
					<js-uploadfile  v-model="model.dataMap" maxCount="0"  imageMaxWidth="-1" imageMaxHeight="-1" :biz-key="model.id" biz-type="carddYcddrw_file_zsf"></js-uploadfile>
				</u-form-item>
			</view>
			
			<view style="display: flex;align-items: center;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="误餐费:" prop="ycddrwWcf" label-width="170" >
					<u-input placeholder=" " v-model="model.ycddrwWcf" :disabled="true" type="digit"  di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="误餐凭据:" prop="ycddrwSjgls" label-width="170" >
					<js-uploadfile  v-model="model.dataMap" maxCount="0"  imageMaxWidth="-1" imageMaxHeight="-1" :biz-key="model.id" biz-type="carddYcddrw_file_wcf"></js-uploadfile>
				</u-form-item>
			</view>
			
			<view style="display: flex;align-items: center;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="停车费:" prop="ycddrwTcf" label-width="170" >
					<u-input placeholder=" " v-model="model.ycddrwTcf" :disabled="true" type="digit"  di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="停车凭据:" prop="ycddrwSjgls" label-width="170" >
					<js-uploadfile  v-model="model.dataMap" maxCount="0"  imageMaxWidth="-1" imageMaxHeight="-1" :biz-key="model.id" biz-type="carddYcddrw_file_tcf"></js-uploadfile>
				</u-form-item>
			</view>
			<view style="display: flex;align-items: center;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="洗车费:" prop="ycddrwXcf" label-width="170" >
					<u-input placeholder=" " v-model="model.ycddrwXcf" :disabled="true" type="digit" di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="洗车凭据:" prop="ycddrwSjgls" label-width="170" >
					<js-uploadfile  v-model="model.dataMap" maxCount="0"  imageMaxWidth="-1" imageMaxHeight="-1" :biz-key="model.id" biz-type="carddYcddrw_file_xcf"></js-uploadfile>
				</u-form-item>
			</view>
			<view style="display: flex;align-items: center;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="交通费:" prop="ycddrwJtf" label-width="170" >
					<u-input placeholder=" " v-model="model.ycddrwJtf" :disabled="true" type="digit" di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="交通凭据:" prop="ycddrwSjgls" label-width="170" >
					<js-uploadfile  v-model="model.dataMap" maxCount="0"  imageMaxWidth="-1" imageMaxHeight="-1" :biz-key="model.id" biz-type="carddYcddrw_file_jtf"></js-uploadfile>
				</u-form-item>
			</view>
			<view style="display: flex;align-items: center;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="车检费:" prop="ycddrwCjf" label-width="170" >
					<u-input placeholder=" " v-model="model.ycddrwCjf" :disabled="true" type="digit" di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="车检凭据:" prop="ycddrwSjgls" label-width="170" >
					<js-uploadfile  v-model="model.dataMap" maxCount="0"  imageMaxWidth="-1" imageMaxHeight="-1" :biz-key="model.id" biz-type="carddYcddrw_file_cjf"></js-uploadfile>
				</u-form-item>
			</view>
			<view style="display: flex;align-items: center;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="矿泉水费:" prop="ycddrwKqsf" label-width="170" >
					<u-input placeholder=" " v-model="model.ycddrwKqsf" :disabled="true" type="digit" di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="矿泉水凭据:" prop="ycddrwSjgls" label-width="170" >
					<js-uploadfile  v-model="model.dataMap" maxCount="0"  imageMaxWidth="-1" imageMaxHeight="-1" :biz-key="model.id" biz-type="carddYcddrw_file_kqs"></js-uploadfile>
				</u-form-item>
			</view>
			<view style="display: flex;align-items: center;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="充电费:" prop="ycddrwCdf" label-width="170" >
					<u-input placeholder=" " v-model="model.ycddrwCdf" :disabled="true" type="digit" di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="充电费凭据:" prop="ycddrwSjgls" label-width="170" >
					<js-uploadfile  maxCount="0" v-model="model.dataMap11"  imageMaxWidth="-1" imageMaxHeight="-1" :biz-key="model.id" biz-type="carddYcddrw_file_cdf"></js-uploadfile>
				</u-form-item>
			</view>
			<view style="display: flex;align-items: center;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="其他费用:" prop="ycddrwQtfy" label-width="170" >
					<u-input placeholder=" " v-model="model.ycddrwQtfy" :disabled="true" type="digit" di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="其他凭据:" prop="ycddrwSjgls" label-width="170" >
					<js-uploadfile  v-model="model.dataMap" maxCount="0"  imageMaxWidth="-1" imageMaxHeight="-1" :biz-key="model.id" biz-type="carddYcddrw_file_qt"></js-uploadfile>
				</u-form-item>
			</view>
	
			
			
			<u-form-item label="其他费用描述:" prop="ycddrwQtfyms" label-width="230" >
				<u-input placeholder="  " v-model="model.ycddrwQtfyms" :disabled="true" type="text" di maxlength="64"></u-input>
			</u-form-item>
			
			
			
			<view style="display: flex;align-items: center;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="超里程数:" prop="ycddrwClcs" label-width="170" >
					<u-input placeholder=" " v-model="model.ycddrwClcs" :disabled="true" type="digit" di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="超里程金额:" prop="ycddrwClcje" label-width="170" >
					<u-input placeholder=" " v-model="model.ycddrwClcje" :disabled="true" type="text"  di maxlength="64"></u-input>
				</u-form-item>
			</view>
			<view style="display: flex;align-items: center;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="超时时间:" prop="ycddrwCssj" label-width="170" >
					<u-input placeholder=" " v-model="model.ycddrwCssj" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="超时金额:" prop="ycddrwCsje" label-width="170" >
					<u-input placeholder=" " v-model="model.ycddrwCsje" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
			</view>
			<view style="display: flex;align-items: center;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="车费:" prop="ycddrwCf" label-width="170" >
					<u-input placeholder=" " v-model="model.ycddrwCf" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="支出合计:" prop="ycddrwZchj" label-width="170" >
					<u-input placeholder=" " v-model="model.ycddrwZchj" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
			</view>
			<u-form-item  label="合计金额:" prop="ycddrwHjje" label-width="170" >
				<u-input placeholder=" " v-model="model.ycddrwHjje" type="text" :disabled="true" di maxlength="64"></u-input>
			</u-form-item>
			
		</u-form>
		
		
		
		
		
		<!-- <view style="height: 250rpx;"></view>
		
		<view class="footer" style="padding: 20rpx 40rpx;">
			<view style=" display: flex;justify-content: space-between;margin-bottom: 20rpx; ">
				<u-button style="flex: 1;margin-right: 20rpx;"  class="btn" type="primary" @click="handleSubmit('0', model.ycddrwRwzt, '')">暂存</u-button>
				<u-button v-if="model.showRwwcBtn" style="flex: 1;" class="btn" type="primary" @click="handleSubmit('1', model.ycddrwRwzt, '7')">任务完成</u-button>
			</view>
			<view style=" display: flex;justify-content: space-between; ">
				<u-button v-if="model.showQxwcBtn" style="flex: 1;margin-right: 20rpx;" class="btn" type="primary"  @click="handleSubmit('1', model.ycddrwRwzt, '6')">取消完成</u-button>
				<u-button v-if="model.showTjrw2Btn" style="flex: 1;" class="btn" type="primary" @click="handleSubmit('1', model.ycddrwRwzt, '8')">提交任务</u-button>
			</view>
		</view> -->
	
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// value: 
				model:{
					// id:'',
					// carNo:'',
					// planDate:'',
					// cdriver:'',
					// driverPhone:'',
					// carVenCode:'',
					// carVenName:'',
					// remarks:'',
				},
				carVenSelectList: [],
				ycddrwDyrqTime:false,
				params: {
					year: true,
					month: true,
					day: true,
					// hour: true,
					// minute: true,
					// second: true
				},
				children:[],
				pickerTime: false,//控制日期显示
			}
		},
		onLoad(params){
			// this.model = JSON.parse(params.model)
			this.model = params
			// this.$u.api.car.carddYcddrwlform({id:params.id}).then(res => {
			// 	this.model = res.carddYcddrw
				
			// 	this.children = res.carddYcddrw.carddYcddrwBtList
			// });
			
			// uni.$on('detailsData', (data)=>{
			// 	console.log(data);
			// 	if(data.index || data.index==0){
			// 		this.children[data.index] = data
			// 		this.$forceUpdate()
			// 	}else{
			// 		this.children.push(data)
			// 	}
				
			// });
			
		},
		onShow() {
			// this.children =  []
			this.$u.api.car.carddYcddrwlform({id:this.model.id}).then(res => {
				this.model = res.carddYcddrw
				
				this.children = res.carddYcddrw.carddYcddrwBtList
				console.log(this.children,'this.children===');
			});
		},
		onReady() {
			// // 运输单位数据
			// this.$u.api.carVen.treeData().then(res => {
			// 	this.carVenSelectList = res;
			// });
		},
		methods: {
			delDetail(item, index) {
				let that = this
				uni.showModal({
					title: '删除提示',
					content: '是否删除此货位信息？',
					confirmColor: '#F54E40',
					success: (res) => {
						if (!res.confirm) {
							return false;
						}
			
						// that.children.splice(index, 1);
						// item.status = '1'
						this.$u.api.car.carddYcddrwldeleteBt({id:item.id}).then(res => {
							if(res.result == 'true'){
								this.$u.toast(res.message);
								this.children.splice(index, 1);
							}else{
								this.$refs.jsError.showError('',res.message,'error');
							}
						})
						that.$forceUpdate()
					}
				})
			},
			handleSubmit(ope, oldStatus, newStatus){
				let data = {
					...this.model,
					ope,
					oldStatus,
					newStatus
				}
				data.carddYcddrwBtList = this.children
				this.$u.api.car.carddYcddrwlsave(data).then(res => {
					if(res.result == 'true'){
						this.$u.toast(res.message);
						setTimeout(()=>{
							uni.navigateBack({
								delta: 1
							})
						},500)
					}else{
						this.$refs.jsError.showError('',res.message,'error');
					}
				})
			},
			callNumber(phone) {
				let that = this
				if(phone){
					uni.makePhoneCall({
						phoneNumber: phone,
						success: () => {
							console.log('拨打电话成功！');
						},
						fail: () => {
							that.$u.toast('拨打电话失败！');
							// console.error('拨打电话失败！');
						}
					});
				}
			},
			ycddrwDyrqConfirm(e) {
				//+ " " + e.hour+ ":" + e.minute + ":" + e.second
				this.model.ycddrwDyrq = e.year + "-" + e.month + "-" + e.day 
				console.log(this.model,'44455566');
			},
			edit(item,index){
				// item.index = index
				// item.['parentId.id'] = this.model.id
				let obj = {
					...item,
					'parentId.id':this.model.id
				}
				// uni.navigateTo({
				// 	url: "/pages/index/detailsForm2?item=" + JSON.stringify(obj),
				// });
				uni.navigateTo({
					url: "/pages/index/detailsForm2",
					success: function(res1) {
							// 通过eventChannel向被打开页面传送数据
							// 其中含有两个参数，第一个是接收的函数名，第二个则是需要携带的参数
						res1.eventChannel.emit('detailsForm2', obj)
					}
				});
			},
			GoaddChild(){
				let obj = {
					parentId:{
						id:this.model.id,
					}
				}
				uni.navigateTo({
					url: "/pages/index/detailsForm2?item=" + JSON.stringify(obj),
				});
			},
			renwu(){},
			chejian(){
				// uni.navigateTo({
				// 	url: "/pages/index/form2?id=" + this.model.id,
				// });
			},
			baocun(){},
			quxiao(){
				uni.navigateBack({
					delta: 1
				})
			},
			showKeyboard(ref){
				this.$refs[ref].toShow(this.model.carNo)
			},
			timeConfirm(e){
				this.model.planDate = e.year + '-' + e.month + '-' + e.day +' '+e.hour +":"+e.minute +":"+e.second;
			},
			submit(data, callback) {
				if(this.model.carNo == null || this.model.carNo == ''){
					this.$refs.jsError.showError('','请正确填入车牌号！','error');
					return;
				}else if(this.model.planDate == null || this.model.planDate == ''){
					this.$refs.jsError.showError('','请正确选择计划发货日期！','error');
					return;
				}else if(this.model.carVenCode == null || this.model.carVenCode == ''){
					this.$refs.jsError.showError('','请正确选择运输单位！','error');
					return;
				}else if(this.model.ipicture == undefined || this.model.ipicture == ''){
					this.$refs.jsError.showError('','请必填图片是否可再次上传！','error');
					return;
				}else{
					var data = {
						mfCarplanFhH: JSON.stringify(this.model),
						useStatus: 1
					};
					this.$u.api.mffh.save(data).then(res => {
						if(res.result == 'true'){
							this.$u.toast(res.message);
							setTimeout(()=>{
								uni.$emit('refreshData');
								uni.navigateBack({
									delta: 1
								})
							},500)
						}else{
							this.$refs.jsError.showError('',res.message,'error');
						}
					});
				}
			},
		}
	}
</script>
<style scoped  lang="less">
	
.footer {
	position: fixed;
	left: 0;
	bottom: 0;
	width: 100%;
	background: #fff;z-index: 999;
	border-top: 1px solid #aaa;
}

.cu-bar {
		min-height: 60px;
	}

	.cu-btn {
		font-size: 16px;
		height: 40px;
	}

	.foot {
		background: #fff;
	}

	.cu-item {
		flex-wrap: wrap;
		padding: 8px 15px !important;
	}

	.title {
		width: 220rpx;
	}

	.cu-modal-slot {
		margin: 42rpx 32rpx 20rpx 32rpx;
		height: 100vh;
		position: relative;
	}

	.cu-modal-header {
		font-size: 38rpx;
		color: #3d3d3d;
		line-height: 40rpx;
		text-align: left;
		font-weight: 500;
	}

	.button {
		font-size: 32rpx;
		color: #666666;
		line-height: 40rpx;
		padding: 12rpx 40rpx;
		margin-bottom: 20rpx;
		background: #e2e2e2;
		border-radius: 180rpx;
	}

	.button:hover {
		background: #3e97b0;
		color: #ffffff;
	}

	.cu-modal-search-title {
		margin: 32rpx 0rpx 20rpx 0rpx;
		text-align: left;
		font-size: 32rpx;
		color: #999999;
		line-height: 40rpx;
	}

	.cu-dialog {
		background: #ffffff;
	}

	.cu-modal-footer {
		padding: 32rpx 32rpx;
		width: 100%;

		.cu-btn {
			width: 50%;
		}
	}

	.bg-confirm {
		background: #3e97b0;
		color: #ffffff;
	}
</style>