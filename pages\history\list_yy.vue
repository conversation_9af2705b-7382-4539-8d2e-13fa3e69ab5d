<template>
	<view class="container">
		<js-error mode="bottom" ref="jsError"></js-error>
		<view class="search">
			<u-search @click="show = true" :show-action="false" placeholder="搜索" :disabled="true"></u-search>
		</view>
		<view>
			<u-popup v-model="show" mode="top" length="50%">
				<view class="text-blue padding-left-sm padding-top-sm" style="font-size: 18px;padding: 10px 10px 0px;">
					<u-icon name="/static/common/img/find.png" size="64"></u-icon>
					<text class="padding-left-sm text-blue" >查询条件</text>
				</view>
				<u-form v-if="show" class="form" :model="query" ref="uForm" label-position="left">
					
					<!-- <u-form-item class="text-bold" label="预约医院:" prop="ccode" label-width="200">
						<u-input placeholder="请输入预约医院" v-model="query.ccode" type="text" maxlength="200"></u-input>
					</u-form-item> -->
					
					<u-form-item class="text-bold" label="预约日期:" prop="createDate" label-width="200">
						<!-- <u-input placeholder="请选择日期" v-model="query.ddate" type="text" maxlength="200"></u-input> -->
						<u-input placeholder="请选择预约日期" v-model="query.createDate" type="select" :select-open="startTime"
							@click="startTime = true"></u-input>
					</u-form-item>
					
					<u-form-item class="text-bold" label="预约状态:" prop="status" label-width="200">
						<js-select v-model="query.status" dict-type="yf_pre_status" placeholder="请选择预约状态"></js-select>
					</u-form-item>
					
					<!-- <u-picker mode="time" v-model="startTime" @confirm="startConfirm"></u-picker> -->
				</u-form>
				<view class="footer">
					<view  style="margin-bottom: 10x;">
						<u-button class="btn" type="primary" @click="submit" >查询</u-button>
					</view>
					<u-button class="btn margin-top-lg" @click="reset">重置</u-button>
				</view>
			</u-popup>
			<u-calendar v-model="startTime" mode="range" @change="startConfirm" max-date="9999"></u-calendar>
		</view>

		<scroll-view class="scroll-list" scroll-y="true" @scrolltolower="loadMore" :refresher-enabled="true"
			:refresher-triggered="triggered" @refresherrefresh="refresherrefresh" style="background-color: #eee;">
			<!-- 判断warehouseTypeNormal为0时的点击事件 -->
			<view v-if="list.length" class="tolView" v-for="(item, index) in list" :key="item.id"
				@click="detailsTr(item)">
				<view class="text" style="color: #000;display: flex;justify-content: space-between;margin-bottom: 5px;">
					<view style="margin: auto 0;flex: 1;display: flex;">
						<view style="width: 80px;">
							<view style=" display: inline-block;padding: 5px;background: #00aaff;color: #fff;width: 40px;text-align: center;font-style: italic;font-weight: bold">
								{{ index + 1 }}
							</view>
						</view>
						<!-- <text class="text-bold">【{{ index + 1 }}】</text> -->
						<text class="text-bold" style="font-size: 30rpx;font-weight: bold;">{{item.createDate}}</text>
					</view>
					
				
					<div style="width: 150rpx;">
						<dictLabel style="margin-left: 10px;" :value="item.status" dict-type="yf_pre_status">
						</dictLabel>
					</div>
					
				</view>
				<!-- <view style="margin-bottom: 5px; display: flex;font-size: 30rpx;font-weight: bold;"><text style="width: 80px;">预约时间：</text>
					<text style="flex: 1;"> {{item.createDate}} </text>
				</view> -->
				<view style="margin-bottom: 5px; display: flex"><text style="width: 80px;">预约人：</text>
					<text style="flex: 1;"> {{item.creator.userName}}</text>
				</view>
				<view v-if="item.status == '0'" style="margin-bottom: 5px; display: flex"><text style="width: 80px;">预计收运：</text>
					<text style="flex: 1;"> {{item.planDate?item.planDate:''}} </text>
				</view>
				<view v-if="item.status == '2'" style="margin-bottom: 5px; display: flex"><text style="width: 80px;">驾驶员：</text>
					<text style="flex: 1;"> {{item.driverObj?item.driverObj.userName:''}} </text>
				</view>
				<view v-if="item.status == '2'" style="margin-bottom: 5px; display: flex"><text style="width: 80px;">押运员：</text>
					<text style="flex: 1;"> {{item.driver2Obj?item.driver2Obj.userName:''}} </text>
				</view>
				<view v-if="item.status == '2'" style="margin-bottom: 5px; display: flex"><text style="width: 80px;">车牌号：</text>
					<text style="flex: 1;"> {{item.carNo?item.carNo:''}} </text>
				</view>
				<!-- <view style="margin-bottom: 5px; display: flex"><text style="width: 80px;">预约备注：</text>
					<text style="flex: 1;"> {{item.remarks?item.remarks:''}} </text>
				</view> -->
				
				
			</view>
			<view v-if="list.length" class="loadmore">
				<u-loadmore :status="loadStatus"></u-loadmore>
			</view>
			<u-empty v-if="!list.length" text="暂无数据" mode="data"></u-empty>
		</scroll-view>
		
	</view>
</template>

<script>
	import dictLabel from "@/components/dictLabel.vue";
	export default {
		components: {
			dictLabel,
		},
		data() {
			return {
				triggered: false,
				startTime: false, //控制日期显示
				show: false,
				smshow: false,
				focus: true,
				barCode: "",
				list: [
				],
				query: {
					pageNo: 1,
					pageSize: 20,
					orderBy:'a.create_date desc'
				},
				loadStatus: "loadmore",
				x: 650, //x坐标
				y: 650, //y坐标
				TASK_BUS_TYPE: "0", //任务类型
			};
		},
		
		onLoad(e) {
			// this.query.whType = this.$store.state.kst.wareType;
			// this.TASK_BUS_TYPE = e.code;
			// this.taskBusType = e.code;
			// this.loadData();
		},
		onShow() {
			this.query.pageNo = 1;
			this.loadData();
		},
		methods: {
			startConfirm(e) {
				// this.query.createDate_gte = e.year + "-" + e.month + "-" + e.day;
				this.query.createDate = e.startDate + ' 至 ' + e.endDate ;
				this.query.createDate_gte = e.startDate
				this.query.createDate_lte = e.endDate ;
			},
			reset() {
				this.query = {
					pageNo: 1,
					pageSize: 20,
				};
				this.loadData();
			},
			jump() {
				this.barCode = "";
				this.smshow = true;
			},
			
			addcg(item) {
				uni.navigateTo({
					url: "/pages/asd/index/hw/hw?cinvcode=" + item.cinvcode,
				});
			},
			loadMore() {
				this.loadStatus = "loading";
				setTimeout(() => {
					this.query.pageNo += 1;
					this.loadData("add");
				}, 100);
			},
			async refresherrefresh() {
				this.triggered = true;
				this.query.pageNo = 1;
				await this.loadData();
				setTimeout(() => {
					this.triggered = false;
				}, 500);
				// this.triggered = false;
			},
			loadData(type) {
				this.$u.api.yysh.yfPreRecordListData(this.query).then((res) => {
					this.show = false;
					if (res.list.length < 20 || res.list.length == 0) {
						this.loadStatus = "nomore";
					}
					var data = res.list;
					if (type == "add") {
						for (var i = 0; i < data.length; i++) {
							this.list.push(data[i]);
						}
					} else {
						this.list = data;
					}
					
				});
			},
			submit() {
				setTimeout(() => {
					this.query.pageNo = 1;
					this.loadData();
				}, 100);
			},
			jumpBind() {
				let arr = [];
				this.list.forEach((item) => {
					if (item.check) {
						arr.push(item);
					}
				});
				if (arr.length == 0) {
					this.$u.toast("请先选择任务");
					return;
				}
				console.log(arr, "arr");
				uni.navigateTo({
					url: "/pages/wms/cgsj/boxDetail",
				});
			},
		},
	};
</script>

<style lang="scss">
	$all_width: 96rpx;
	$all_height: 96rpx;

	.movable-area2 {
		z-index: 999;
		height: 97vh;
		width: 650rpx;
		position: fixed;
		right: $all_width;
		top: -($all_height + 220rpx);
		pointer-events: none; //此处要加，鼠标事件可以渗透

		.movable-view {
			width: $all_width;
			height: $all_height;
			pointer-events: auto; //恢复鼠标事件

			image {
				width: $all_width;
				height: $all_height;
			}
		}
	}

	.movable-area1 {
		z-index: 999;
		height: 97vh;
		width: 650rpx;
		position: fixed;
		right: $all_width;
		top: -($all_height + 70rpx);
		pointer-events: none; //此处要加，鼠标事件可以渗透

		.movable-view {
			width: $all_width;
			height: $all_height;
			pointer-events: auto; //恢复鼠标事件

			image {
				width: $all_width;
				height: $all_height;
			}
		}
	}

	.movable-area {
		z-index: 888;
		height: 97vh;
		width: 650rpx;
		// max-width: 1152rpx;
		// 固定在右边
		top: -20rpx;
		position: fixed;
		right: $all_width;
		// 固定在右下角
		pointer-events: none; //此处要加，鼠标事件可以渗透

		.movable-view {
			width: $all_width;
			height: $all_height;
			pointer-events: auto; //恢复鼠标事件

			image {
				width: $all_width;
				height: $all_height;
			}
		}
	}

	.tableclass {
		border-left: 1px solid rgb(228, 231, 237);
		border-top: 1px solid rgb(228, 231, 237);
		background-color: rgb(255, 255, 255);
		width: 100%;
		box-sizing: border-box;
	}

	.thclass {
		text-align: center;
		padding: 5px 3px;
		border-bottom: 1px solid rgb(228, 231, 237);
		border-right: 1px solid rgb(228, 231, 237);
		flex-direction: column;
		flex: 1;
		justify-content: center;
		font-size: 14px;
		color: #303133;
		font-weight: bold;
		background-color: #f5f6f8;
	}

	.trclass {
		height: 52px;
	}

	.tdclass {
		padding: 5px 3px;
		border-bottom: 1px solid rgb(228, 231, 237);
		border-right: 1px solid rgb(228, 231, 237);
		flex-direction: column;
		flex: 1;
		justify-content: center;
		font-size: 14px;
		color: #505256;
		align-self: stretch;
		box-sizing: border-box;
		height: 100%;
	}

	.box .item {
		margin: 0 0px 0px;
	}

	.tui-line-cell {
		width: 100%;
		box-sizing: border-box;
		display: flex;
		align-items: center;
	}

	.tui-title {
		line-height: 32rpx;
		min-width: 120rpx;
		flex-shrink: 0;
	}

	.tui-input {
		font-size: 32rpx;
		color: #333;
		padding-left: 20rpx;
		flex: 1;
		overflow: visible;
	}

	.footer {
		position: fixed;
		left: 0;
		bottom: 20px;
		width: 100%;
	}

	.edit {
		position: absolute;
		right: 195rpx;
		top: 0;
		margin-right: 0;
	}

	.add {
		position: absolute;
		right: 130rpx;
		top: 0;
		margin-right: 0;
	}

	.upload {
		position: absolute;
		right: 65rpx;
		top: 0;
		margin-right: 0;
	}

	.rights {
		position: absolute;
		/* display: inline-block; */
		right: 0;
		top: 0;
		margin-right: 0;
		/* width: 100%; */
	}

	.addPlan {
		position: absolute;
		right: 60rpx;
		top: -40rpx;
	}

	.tolView {
		background-color: #fff;
		margin: 10px;
		padding: 10px;
		border-radius: 10px;
	}

	.checkRenWu {
		background: #014e3c;
		color: #fff;
	}
</style>