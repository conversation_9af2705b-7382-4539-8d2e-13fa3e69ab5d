<template>
	<view class="wrap">
		<js-error mode="bottom" ref="jsError"></js-error>
		
		<u-picker mode="time" :params="params" v-model="startTime" @confirm="yfgzRqConfirm"></u-picker>
		<u-form class="form bg-white" :model="model"  ref="uForm" label-position="left" style="margin-top: -20px;">
			<u-form-item class="text-bold" label="日期:" prop="createDate" label-width="200">
				<u-input placeholder="请选择" v-model="yfgzRq" type="select" :select-open="startTime"
					@click="startTime = true"></u-input>
			</u-form-item> 
			
			<view style="display: flex;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="底薪:" prop="jbgzJbgz" label-width="150" >
					<u-input placeholder=" " v-model="model.jbgzJbgz" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="全勤奖:" prop="jbgzQqj" label-width="170" >
					<u-input placeholder=" " v-model="model.jbgzQqj" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
			</view>
			
			<view style="display: flex;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="电话补贴:" prop="jbgzDhbt" label-width="150" >
					<u-input placeholder=" " v-model="model.jbgzDhbt" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="交通补贴:" prop="jbgzJtbt" label-width="170" >
					<u-input placeholder=" " v-model="model.jbgzJtbt" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
			</view>
			
			<view style="display: flex;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="洗车补贴:" prop="jbgzXcbt" label-width="150" >
					<u-input placeholder=" " v-model="model.jbgzXcbt" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="任务奖金:" prop="jjhj" label-width="170" >
					<u-input placeholder=" " v-model="model.jjhj" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
			</view>
			<u-form-item  label="无违章投诉补贴:" prop="jbgzWwztsbt" label-width="230" >
				<u-input placeholder=" " v-model="model.jbgzWwztsbt" type="text" :disabled="true" di maxlength="64"></u-input>
			</u-form-item>
			
			<view style="display: flex;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="养老保险:" prop="jbgzYlbxgryj" label-width="150" >
					<u-input placeholder=" " v-model="model.jbgzYlbxgryj" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="失业保险:" prop="jbgzSyxgryj" label-width="170" >
					<u-input placeholder=" " v-model="model.jbgzSyxgryj" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
				
			</view>
			<view style="display: flex;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="工伤保险:" prop="jbgzGsbxgryj" label-width="150" >
					<u-input placeholder=" " v-model="model.jbgzGsbxgryj" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="基本医疗:" prop="jbgzJbylgryj" label-width="170" >
					<u-input placeholder=" " v-model="model.jbgzJbylgryj" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
				
			</view>
			
			<view style="display: flex;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="大额医疗:" prop="jbgzDeylbzgryj" label-width="150" >
					<u-input placeholder=" " v-model="model.jbgzDeylbzgryj" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="生育保险:" prop="jbgzSybxgryj" label-width="170" >
					<u-input placeholder=" " v-model="model.jbgzSybxgryj" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
			</view>
			<u-form-item  label="实发工资:" prop="yfgz" label-width="170" >
				<u-input placeholder=" " v-model="model.yfgz" type="text" :disabled="true" di maxlength="64"></u-input>
			</u-form-item>
			
		</u-form>
	
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// value: 
				yfgzRq:'',
				model:{
					// id:'',
					// carNo:'',
					// planDate:'',
					// cdriver:'',
					// driverPhone:'',
					// carVenCode:'',
					// carVenName:'',
					// remarks:'',
				},
				carVenSelectList: [],
				startTime: false,
				ycddrwDyrqTime:false,
				params: {
					year: true,
					month: true,
					// day: true,
					// hour: true,
					// minute: true,
					// second: true
				},
				children:[],
				pickerTime: false,//控制日期显示
			}
		},
		onLoad(params){
			// this.model = JSON.parse(params.model)
			// this.model = params
		},
		onShow() {
			const currentDate = new Date();
			const year = currentDate.getFullYear();
			const month = currentDate.getMonth() + 1;
			this.yfgzRq = `${year}-${month.toString().padStart(2, '0')}`;
			
			this.$u.api.car.carddYfgzAppData({yfgzRq:this.yfgzRq}).then(res => {
				if(res.result == 'true'){
					this.model = res.data
				}else{
					this.$u.toast(res.message);
					this.model = {}
				}
				
			});
		},
		onReady() {
			// // 运输单位数据
			// this.$u.api.carVen.treeData().then(res => {
			// 	this.carVenSelectList = res;
			// });
		},
		methods: {
			delDetail(item, index) {
				let that = this
				uni.showModal({
					title: '删除提示',
					content: '是否删除此货位信息？',
					confirmColor: '#F54E40',
					success: (res) => {
						if (!res.confirm) {
							return false;
						}
			
						// that.children.splice(index, 1);
						// item.status = '1'
						this.$u.api.car.carddYcddrwldeleteBt({id:item.id}).then(res => {
							if(res.result == 'true'){
								this.$u.toast(res.message);
								this.children.splice(index, 1);
							}else{
								this.$refs.jsError.showError('',res.message,'error');
							}
						})
						that.$forceUpdate()
					}
				})
			},
			handleSubmit(ope, oldStatus, newStatus){
				let data = {
					...this.model,
					ope,
					oldStatus,
					newStatus
				}
				data.carddYcddrwBtList = this.children
				this.$u.api.car.carddYcddrwlsave(data).then(res => {
					if(res.result == 'true'){
						this.$u.toast(res.message);
						setTimeout(()=>{
							uni.navigateBack({
								delta: 1
							})
						},500)
					}else{
						this.$refs.jsError.showError('',res.message,'error');
					}
				})
			},
			yfgzRqConfirm(e) {
				//+ "-" + e.day + " " + e.hour+ ":" + e.minute + ":" + e.second
				this.yfgzRq = e.year + "-" + e.month 
				this.$u.api.car.carddYfgzAppData({yfgzRq:this.yfgzRq}).then(res => {
					if(res.result == 'true'){
						this.model = res.data
					}else{
						this.$u.toast(res.message);
						this.model = {}
					}
					
				});
				console.log(this.model,'44455566');
			},
			edit(item,index){
				// item.index = index
				// item.['parentId.id'] = this.model.id
				let obj = {
					...item,
					'parentId.id':this.model.id
				}
				uni.navigateTo({
					url: "/pages/index/detailsForm2?item=" + JSON.stringify(obj),
				});
			},
			GoaddChild(){
				let obj = {
					parentId:{
						id:this.model.id,
					}
				}
				uni.navigateTo({
					url: "/pages/index/detailsForm2?item=" + JSON.stringify(obj),
				});
			},
			renwu(){},
			chejian(){
				// uni.navigateTo({
				// 	url: "/pages/index/form2?id=" + this.model.id,
				// });
			},
			baocun(){},
			quxiao(){
				uni.navigateBack({
					delta: 1
				})
			},
			showKeyboard(ref){
				this.$refs[ref].toShow(this.model.carNo)
			},
			timeConfirm(e){
				this.model.planDate = e.year + '-' + e.month + '-' + e.day +' '+e.hour +":"+e.minute +":"+e.second;
			},
			submit(data, callback) {
				if(this.model.carNo == null || this.model.carNo == ''){
					this.$refs.jsError.showError('','请正确填入车牌号！','error');
					return;
				}else if(this.model.planDate == null || this.model.planDate == ''){
					this.$refs.jsError.showError('','请正确选择计划发货日期！','error');
					return;
				}else if(this.model.carVenCode == null || this.model.carVenCode == ''){
					this.$refs.jsError.showError('','请正确选择运输单位！','error');
					return;
				}else if(this.model.ipicture == undefined || this.model.ipicture == ''){
					this.$refs.jsError.showError('','请必填图片是否可再次上传！','error');
					return;
				}else{
					var data = {
						mfCarplanFhH: JSON.stringify(this.model),
						useStatus: 1
					};
					this.$u.api.mffh.save(data).then(res => {
						if(res.result == 'true'){
							this.$u.toast(res.message);
							setTimeout(()=>{
								uni.$emit('refreshData');
								uni.navigateBack({
									delta: 1
								})
							},500)
						}else{
							this.$refs.jsError.showError('',res.message,'error');
						}
					});
				}
			},
		}
	}
</script>
<style scoped  lang="less">
	
.footer {
	position: fixed;
	left: 0;
	bottom: 0;
	width: 100%;
	background: #fff;z-index: 999;
	border-top: 1px solid #aaa;
}

.cu-bar {
		min-height: 60px;
	}

	.cu-btn {
		font-size: 16px;
		height: 40px;
	}

	.foot {
		background: #fff;
	}

	.cu-item {
		flex-wrap: wrap;
		padding: 8px 15px !important;
	}

	.title {
		width: 220rpx;
	}

	.cu-modal-slot {
		margin: 42rpx 32rpx 20rpx 32rpx;
		height: 100vh;
		position: relative;
	}

	.cu-modal-header {
		font-size: 38rpx;
		color: #3d3d3d;
		line-height: 40rpx;
		text-align: left;
		font-weight: 500;
	}

	.button {
		font-size: 32rpx;
		color: #666666;
		line-height: 40rpx;
		padding: 12rpx 40rpx;
		margin-bottom: 20rpx;
		background: #e2e2e2;
		border-radius: 180rpx;
	}

	.button:hover {
		background: #3e97b0;
		color: #ffffff;
	}

	.cu-modal-search-title {
		margin: 32rpx 0rpx 20rpx 0rpx;
		text-align: left;
		font-size: 32rpx;
		color: #999999;
		line-height: 40rpx;
	}

	.cu-dialog {
		background: #ffffff;
	}

	.cu-modal-footer {
		padding: 32rpx 32rpx;
		width: 100%;

		.cu-btn {
			width: 50%;
		}
	}

	.bg-confirm {
		background: #3e97b0;
		color: #ffffff;
	}
</style>