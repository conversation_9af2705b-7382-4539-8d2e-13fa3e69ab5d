<template>
	<view class="wrap">
		<!-- <js-lang title="home.title" :showBtn="true"></js-lang> -->
		
		<u-sticky>
				    <view style="padding: 10px; margin-bottom: 10px;background: #fff;">
						<view style="padding: 10px; text-align: center;font-weight: bold;font-size: 16px;">
							欢迎使用苏鑫医废处理
						</view>
				    </view>
				</u-sticky>
				<u-swiper :height="500" :list="imgList" :title="false" @click="imgListClick"></u-swiper>
		<!-- <view class="toolbar u-m-b-20" style="margin-top: 10px;">
			<u-grid class="grid" :col="3" :border="false">
				<u-grid-item :index="0" @click="navTo('/pages/sys/user/tz')">
					<u-icon class="grid-icon" name="order" :size="80" :style="{ color: '#ea9a44' }"></u-icon>
					<view class="grid-text">医废台账</view>
				</u-grid-item>
				<u-grid-item :index="1" @click="navTo('/pages/sys/user/yfht')">
					<u-icon class="grid-icon" name="order" :size="80" :style="{ color: '#47cb66' }"></u-icon>
					<view class="grid-text">医废合同</view>
				</u-grid-item>
				<u-grid-item :index="2" @click="navTo('/pages/history/list_yy')">
					<u-icon class="grid-icon" name="car" :size="80" :style="{ color: '#5a98ea' }"></u-icon>
					<view class="grid-text">预约记录</view>
				</u-grid-item>
			</u-grid>
		</view> -->
		<view class="u-m-t-20">
			<u-cell-group>
					<u-cell-item  icon="order" :iconSize="iconSize" :iconStyle="{ color: '#1a94ff' }"
						title="医废台账" @click="navTo('/pages/sys/user/tz')"></u-cell-item>
					<u-cell-item  icon="order" :iconSize="iconSize" :iconStyle="{ color: '#1bca6a' }"
						title="医废合同" @click="navTo('/pages/sys/user/yfht')"></u-cell-item>
					<!-- <u-cell-item  icon="order" :iconSize="iconSize" :iconStyle="{ color: '#1bca6a' }"
						title="医废合同" @click="navTo('yfht2')"></u-cell-item> -->
					<!-- <u-cell-item  icon="car" :iconSize="iconSize" :iconStyle="{ color: '#a571fd' }"
						title="预约记录" @click="navTo('/pages/history/list_yy')"></u-cell-item> -->
					<u-cell-item icon="clock" :iconSize="iconSize" :iconStyle="{ color: '#ff6f27' }"
							title="用户注册统计" @click="navTo('/pages/sys/user/zcjl')"></u-cell-item> 
					<!-- <u-cell-item v-if="vuex_user.extend.extendS2 == '02'" icon="car" :iconSize="iconSize" :iconStyle="{ color: '#a571fd' }"
						title="收运记录" @click="navTo('/pages/history/list')"></u-cell-item> -->
			</u-cell-group>
		</view>
	</view>
</template>
<script>
/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 */
export default {
	data() {
		return {
			
			imgList: [
				{image: '/static/jeesite/img/1.jpg'},
				{image: '/static/jeesite/img/5.jpg'},
				{image: '/static/jeesite/img/6.jpg'}
			],
			
			todoCount: 0,
			
			menuList: [
				{
					menuCode: 'a-1',
					menuName: '增删改查',
					menuIcon: 'file-text',
					menuColor: '',
					url: '',
					childList: [
						{
							menuCode: 'a13',
							menuName: '列表',
							menuIcon: 'thumb-up',
							menuColor: '',
							url: '/pages/testData/index',
						},
						{
							menuCode: 'a11',
							menuName: '新增',
							menuIcon: 'plus-circle',
							menuColor: '',
							url: '/pages/testData/form',
						},
						{
							menuCode: 'a10',
							menuName: '请假',
							menuIcon: 'calendar',
							menuColor: '',
							url: '/pages/oa/oaLeave/index',
						},
					]
				},
				{
					menuCode: 'a',
					menuName: '公文管理',
					menuIcon: 'home',
					menuColor: '#919328',
					url: '',
					childList: [
						{
							menuCode: 'a1',
							menuName: '收文',
							menuIcon: 'email',
							menuColor: '#919328',
							url: '/pages/testData/form',
						},
						{
							menuCode: 'a2',
							menuName: '发文',
							menuIcon: 'bookmark',
							menuColor: '#919328',
							url: '/pages/testData/form',
						},
						{
							menuCode: 'a3',
							menuName: '查询',
							menuIcon: 'search',
							menuColor: '#919328',
							url: '/pages/testData/index',
						}
					]
				},
				{
					menuCode: 'a-2',
					menuName: '功能列表',
					menuIcon: '',
					menuColor: '#0d9311',
					url: '',
					childList: [
						{
							menuCode: 'a21',
							menuName: '找回密码',
							menuIcon: '',
							menuColor: '#0d9311',
							url: '/pages/sys/login/forget',
						},
						{
							menuCode: 'a22',
							menuName: '注册用户',
							menuIcon: '',
							menuColor: '#0d9311',
							url: '/pages/sys/login/reg',
						},
						{
							menuCode: 'a23',
							menuName: '个人资料',
							menuIcon: '',
							menuColor: '#0d9311',
							url: '/pages/sys/user/info',
						},{
							menuCode: 'a24',
							menuName: '关于我们',
							menuIcon: '',
							menuColor: '#0d9311',
							url: '/pages/sys/user/about',
						},
						{
							menuCode: 'a25',
							menuName: '修改密码',
							menuIcon: '',
							menuColor: '#0d9311',
							url: '/pages/sys/user/pwd',
						},
						{
							menuCode: 'a26',
							menuName: '意见反馈',
							menuIcon: '',
							menuColor: '#0d9311',
							url: '/pages/sys/user/comment',
						},
						{
							menuCode: 'a27',
							menuName: '系统设置',
							menuIcon: '',
							menuColor: '#0d9311',
							url: '/pages/sys/user/setting',
						},
						{
							menuCode: 'a28',
							menuName: '列表演示',
							menuIcon: '',
							menuColor: '#0d9311',
							url: '/pages/testData/index',
						},
						{
							menuCode: 'a29',
							menuName: '表单演示',
							menuIcon: '',
							menuColor: '#0d9311',
							url: '/pages/testData/form',
						}
					]
				},
			],
				
		};
	},
	onLoad() {
		//this.refreshCount();
	},
	onShow() {
		// this.refreshCount();
	},
	methods: {
		navTo(url) {
			uni.navigateTo({
				url: url
			});
		},
		refreshCount() {
			this.todoCount = 3;
		},
		imgListClick(index) {
			console.log(`点击了第${index + 1}页图片`)
		},
		itemClick(index) {
			console.log(index);
		}
	}
};
</script>
<style lang="scss">
@import 'index.scss';
page {
	background-color: #f8f8f8;
}
</style>
