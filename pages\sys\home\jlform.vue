<template>
  <view class="wrap">
    <js-error mode="bottom" ref="jsError"></js-error>
    <view style="background-color: #fff;">
      <u-form
        class="form bg-white"
        :model="model"
        ref="uForm"
        label-position="left"
        style="padding: 0 10px"
      >
		<u-form-item
		  label="医院名称:"
		  prop="hospName"
		  label-width="200"
		  required
		  :label-style="{ 'font-weight': 'bold'}"
		>
		  <!-- <js-select v-model="model.binvBatch" dict-type="sys_yes_no" placeholder="请选择医院"></js-select> -->
			<!-- :disabled='model.id?true:false' -->
			<js-select disabled  :childName="'None'" :showFilter="true" v-model="model.hospCode" :items="selectList"
				placeholder="请选择医院" :tree="false" :label-value="model.hospName" :flag="true"
				@label-input="model.hospName = $event" >
				<!-- @confirm="selectConfirm" -->
			</js-select>
		
		</u-form-item>
		
		<u-form-item
		  label="车牌号:"
		  prop="carNo"
		  label-width="200"
		  required
		  :label-style="{ 'font-weight': 'bold'}"
		>
			<js-select   :childName="'None'" :showFilter="true" v-model="model.carNo" :items="selectCarList"
				placeholder="请选择" :tree="false" :label-value="model.carNo" :flag="true"
				@label-input="model.carNo = $event" >
			</js-select>
		</u-form-item>
		
		<u-form-item
		  label="驾驶员:"
		  prop="driver"
		  label-width="200"
		  required
		  :label-style="{ 'font-weight': 'bold'}"
		>
			<js-select   :childName="'None'" :showFilter="true" v-model="model.driverName" :items="userSelectList"
				placeholder="请选择" :tree="false" :label-value="model.driverName"
				@label-input="model.driverName = $event" >
			</js-select>
		</u-form-item>
		
		<u-form-item label="押运员:"  prop="driver2" label-width="200" :label-style="{ 'font-weight': 'bold'}">
			<js-select :childName="'None'" clearable :showFilter="true" v-model="model.driver2" :items="userSelectList" placeholder="请选择" :tree="false"
				:label-value="model.driver2Name" @label-input="model.driver2Name = $event" ></js-select>
		</u-form-item>
		
		
		
		<u-form-item
		  label="失败原因:"
		  prop="remarks"
		  label-width="200"
		  
		  :label-style="{ 'font-weight': 'bold'}"
		>
		  <u-input v-model="model.remarks" type="textarea"  placeholder="请输入原因"
		  	clearable />
		</u-form-item>
		<u-form-item
		  label="水印照片:"
		  label-width="200"
		  required
		  :label-style="{ 'font-weight': 'bold'}"
		>
		  <js-uploadfile v-model="model.dataMap" :biz-key="model.id" biz-type="yf_cancel_file"></js-uploadfile>
		</u-form-item>

      </u-form>
    </view>
	<view class="form-footer b-btns" style="height: 80px;">
		<view class="footer" >
			<u-button  class="btn" type="primary" @click="submit">提交</u-button>
		</view>
	</view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      stockListHeight: 0,
      showflag: 0,
      model: {
        id: "",
		// qtyGr:0,
		// qtyPs:0,
		// weightGr:0,
		// weightPs:0,
      },
      xmflag: false,
      iqty: "",
      flag: false,
      focus: true,
	  selectList: [],
	  selectCarList: [],
	  userSelectList: [],
      x: 650, //x坐标
      y: 650, //y坐标
    };
  },
  onLoad() {
	  
  	let that = this
  	const eventChannel = this.getOpenerEventChannel();
  	eventChannel.on('acceptDatajlFrom', function(data) {
  		that.model = data
		
		that.model.driverName = data.driverObj?data.driverObj.userName:''
		that.model.driver2Name = data.driver2Obj?data.driver2Obj.userName:''
		that.model =  {...that.model}
  	})
	
	var _self = this;
	uni.getSystemInfo({
	  success: (e) => {
	    // resu 可以获取当前屏幕的高度
	    _self.stockListHeight = e.windowHeight - uni.upx2px(160);
	  },
	  fail: (res) => {},
	});
  },
  watch: {},
  onShow() {
	  this.getLTreeData()
  },
  onReady() {},
  methods: {
	  getLTreeData() {
	  	this.$u.api.yysh.m8ViewHospTreeData({isShowCode: false,bused:1}).then(res => {
	  		this.selectList = res.map(item => {
				item.value = item.id
				return item
			})
	  	})
		
		this.$u.api.yysh.m8ViewCarTreeData({isShowCode: false}).then(res => {
			this.selectCarList = res.map(item => {
				item.value = item.id
				return item
			})
		})
		
		// 人员和机构数据
		// this.$u.api.office.treeData({isLoadUser: true}).then(res => {
		// 	this.userSelectList = res;
		// });
		this.$u.api.yysh.empUserTreeData({officeCode:'01002'}).then(res => {
			this.userSelectList =  res.map(item => {
				item.value = item.id
				return item
			})
			this.userSelectList.unshift({value:'',name:''})
		});
	  },
	  replaceInput(e,type) {
	  	var that = this
	  	e = e.match(/^\d*(\.?\d{0,2})/g)[0]
	  	this.$nextTick(() => {
			if(type == '1'){
				that.model.qtyGr = e
			}
			if(type == '2'){
				that.model.qtyPs = e
			}
			if(type == '3'){
				that.model.qtySum = e
			}
			if(type == '4'){
				that.model.weightGr = e
			}
			if(type == '5'){
				that.model.weightPs = e
			}
			if(type == '6'){
				that.model.weightSum = e
			}
	  		
	  	})
	  },
	
    async submit() {
      if (!this.model.hospName) {
        this.$u.toast('请先选择医院！');
        return;
      }
	  if (!this.model.carNo) {
	    this.$u.toast('请先选择车牌号！');
	    return;
	  }
	  
	  if (!this.model.driverName) {
	    this.$u.toast('请先选择驾驶员！');
	    return;
	  }
	  // if (!this.model.remarks ) {
	  //   this.$u.toast('请输入原因！');
	  //   return;
	  // }
	  if (!this.model.dataMap || !this.model.dataMap.yf_cancel_file) {
	    this.$u.toast('请先上传图片！');
	    return;
	  }
	 
      let data = {
        preId: this.model.id?this.model.id:'',
		remarks: this.model.remarks,
		dataMap:this.model.dataMap,
		hospCode:this.model.hospCode,
		hospName:this.model.hospName,
		carNo:this.model.carNo,
		driverName:this.model.driverName,
		driver2Name:this.model.driver2Name,
      };
      this.$u.api.yysh.yfCancelRecord(data).then((res) => {
        if (res.result == "true") {
          this.$u.toast(res.message);
          setTimeout(() => {
            uni.navigateBack({
              delta: 1,
            });
          }, 500);
        } else {
          uni.showModal({
          	title: "提示",
          	content: res.message,
          	showCancel: false,
          	success: (res) => {},
          });
          // this.$refs.jsError.showError("", res.message, "error");
        }
      });
    },

  },
};
</script>
<style lang="scss">
page {
		// background-color: #f8f8f8;
		// background-color: #e6e6e6;
		background-color: #fff;
	}
.footer {
	position: fixed;
	left: 0;
	padding: 0 10px;
	bottom: 0px;
	width: 100%;
	z-index: 999;
	
}
</style>