<template>
	<view v-if="labelData.name" :style="{'display':'inline-block','padding':'5px 10px','border-radius':'5px',
		'background-color':labelData.cssClass,'color':labelData.cssStyle
		}">
		{{labelData.name}}
	</view>
</template>
<script>
/**
 * @property {Object} value 用于取字典的值
 * @property {String} dictType 字典类型，从字典里获取
 * <dictLabel :value="value" dict-type="U8_SU_TH_ROW_STATUS">
 */
export default {
	props: {
		value: {
			type: String,
			default: '0'
		},
		dictType: {
			type: String,
			default: ''
		},
	},
	data() {
		return {
			labelData:{
			},
		};
	},
	watch: {
		value(val, oldVal) {
			this.loadData();
		},
		// dictType(val, oldVal) {
		// 	this.loadData();
		// },
	},
	created() {
		this.loadData();
	},
	methods: {
		loadData() {
			if (this.dictType != ''){
				this.$u.api.dictData({dictType: this.dictType}).then(res => {
					if (typeof res === 'object' && res.result === 'login'){
						return;
					}
					let arr = res.filter(item=>{
						return item.value == this.value
					})
					console.log(arr,'arr===');
					if(!arr.length){
						this.labelData = {
							cssClass:'',
							cssStyle:'',
							name:this.value
						}
					}else{
						this.labelData = arr[0]
						console.log(this.labelData,'this.labelData===');
						
						if(this.labelData.cssClass){
							this.labelData.cssClass = this.labelData.cssClass.slice(4, this.labelData.cssClass.length)
						}
						// 判断this.labelData.cssStyle 是否存在
						if(this.labelData.cssStyle){
							this.labelData.cssStyle = this.labelData.cssStyle.slice(6, this.labelData.cssStyle.length)
							if(this.labelData.cssStyle[this.labelData.cssStyle.length-1] == ';'){
								let str = this.labelData.cssStyle
								this.labelData.cssStyle =str.slice(0, str.length - 1);
							}
						}
						
					}
				});
			}
			
		},
		
	
	}
}
</script>
<style lang="scss" scoped>
	
</style>
