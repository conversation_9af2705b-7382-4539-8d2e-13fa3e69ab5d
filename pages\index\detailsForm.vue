<template>
	<view class="wrap">
		<js-error mode="bottom" ref="jsError"></js-error>
		<!-- <view class="cu-bar bg-white ">
			<view class="action">
				<u-icon name="/static/image/invcode.png" size="80"></u-icon>
				<text class="cuIcon-titles text-bold">发货信息</text> 
			</view>
		</view> -->
		<u-form class="form bg-white" :model="model"  ref="uForm" label-position="left" style="margin-top: -20px;">
			
			<!-- <u-form-item style="flex: 1;" label="日期:" prop="btRq" label-width="230" required>
				<u-input placeholder="请选择" v-model="model.btRq" type="select" class="input-align" 
							@click="btRqTime = true" />
			</u-form-item> -->
			<u-picker mode="time" :params="params" v-model="btRqTime" @confirm="btRqConfirm"></u-picker>
			<u-form-item style="flex: 1;" label="行驶路线:" prop="btXslx" label-width="230" required>
				<u-input placeholder="请输入 " v-model="model.btXslx" type="text"  di maxlength="64"></u-input>
			</u-form-item>
			<u-form-item style="flex: 1;" label="起始路码:" prop="btQslm" label-width="230" required>
				<u-input placeholder="请输入 " @input="btQslmInput" v-model="model.btQslm" type="digit"  di maxlength="64"></u-input>
			</u-form-item>
			<u-form-item style="flex: 1;" label="起始路码照片:" prop="upload" label-width="230" required>
				<js-uploadfile  v-model="model.dataMap" :sourceType="['camera']"  imageMaxWidth="-1" imageMaxHeight="-1" :biz-key="model.id" biz-type="carddYcddrw_file_bt_qs"></js-uploadfile>
			</u-form-item>
			<u-form-item style="flex: 1;" label="结束路码:" prop="btJslm" label-width="230" required>
				<u-input placeholder="请输入 " @input="btJslmInput" v-model="model.btJslm" type="digit"  di maxlength="64"></u-input>
			</u-form-item>
			<u-form-item style="flex: 1;" label="结束路码照片:" prop="upload" label-width="230" required>
				<js-uploadfile  v-model="model.dataMap" :sourceType="['camera']"  imageMaxWidth="-1" imageMaxHeight="-1" :biz-key="model.id" biz-type="carddYcddrw_file_bt_js"></js-uploadfile>
			</u-form-item>
			<u-form-item style="flex: 1;" label="实际公里:" prop="btSjgl" label-width="230" >
				<u-input placeholder=" " :disabled="true" v-model="model.btSjgl" type="text"  di maxlength="64"></u-input>
			</u-form-item>
			<u-picker mode="time" :params="params1" v-model="btScsjTime" @confirm="btScsjConfirm"></u-picker>
			<u-form-item style="flex: 1;" label="上车时间:" prop="btScsj" label-width="230" required>
				<u-input placeholder="请选择" v-model="model.btScsj" type="select" class="input-align" :select-open="startTime"
							@click="btScsjTime = true" />
			</u-form-item>
			<u-picker mode="time" :params="params1" v-model="btXcsjTime" @confirm="btXcsjConfirm"></u-picker>
			<u-form-item style="flex: 1;" label="下车时间:" prop="btXcsj" label-width="230" required>
				<u-input placeholder="请选择" v-model="model.btXcsj" type="select" class="input-align" :select-open="startTime"
							@click="btXcsjTime = true" />
			</u-form-item>
			<u-form-item style="flex: 1;" label="超时时间（小时):" prop="btCssjxs" label-width="230" >
				<u-input placeholder="请输入 " v-model="model.btCssjxs" type="digit"  di maxlength="64"></u-input>
			</u-form-item>
			<u-form-item style="flex: 1;" label="备注:" prop="btBz" label-width="230" >
				<u-input placeholder="请输入 " v-model="model.btBz" type="text" di maxlength="64"></u-input>
			</u-form-item>
		</u-form>
		<view style="height: 120rpx;"></view>
		<!-- <view class="form-footer b-btns">
			<view class="footer">
				<u-button class="btn" type="primary" @click="submit">保存</u-button>
				<u-button class="btn" type="primary" @click="submit">保存</u-button>
				<u-button class="btn" type="primary" @click="submit">保存</u-button>
				<u-button class="btn" type="primary" @click="submit">保存</u-button>
			</view>
		</view> -->
		<view class="footer" style="padding: 20rpx 40rpx;">
			<view style=" display: flex;justify-content: space-between;">
				<u-button style="flex: 1;margin-right: 20rpx;"  class="btn"  @click="quxiao">取消</u-button>
				<u-button style="flex: 1;" class="btn" type="primary" @click="queren">确认</u-button>
			</view>
			
		</view>
		
		<!-- <view class="btn-group cu-bar foot">
			<button class="cu-btn bg-green shadow-blur" @tap="submit()">
				<text class="cuIcon-add"></text> 保存
			</button>
		</view> -->
	</view>
</template>

<script>
	export default {
		data() {
			return {
				params:{
					year: true,
					month: true,
					day: true,
					// hour: true,
					// minute: true,
					// second: true
				},
				params1:{
					year: true,
					month: true,
					day: true,
					hour: true,
					minute: true,
					// second: true
				},
				btRqTime:false,
				btScsjTime:false,
				btXcsjTime:false,
				// value: 
				model:{
					// id:'',
					// carNo:'',
					// planDate:'',
					// cdriver:'',
					// driverPhone:'',
					// carVenCode:'',
					// carVenName:'',
					// remarks:'',
				},
				carVenSelectList: [],
				// params: {
				// 	year: true,
				// 	month: true,
				// 	day: true,
				// 	hour: true,
				// 	minute: true,
				// 	second: true
				// },
				pickerTime: false,//控制日期显示
			}
		},
		onLoad(params){
			// if(params){
			// 	this.model = JSON.parse(params.item)
			// }
			let that = this
			const eventChannel = this.getOpenerEventChannel();
			eventChannel.on('detailsForm', function(data) {
				that.model = data
			})
			
			// this.$u.api.car.carddYcddrwlform({id:params.id}).then(res => {
			// 	this.model = res.carddYcddrw
			// });
		},
		onShow() {
		},
		onReady() {
			// // 运输单位数据
			// this.$u.api.carVen.treeData().then(res => {
			// 	this.carVenSelectList = res;
			// });
		},
		methods: {
			btQslmInput(e){
					if(e && this.model.btJslm){
						this.model.btSjgl =  this.model.btJslm - e
					}else{
						this.model.btSjgl = ''
					}
					console.log(this.model.btSjgl,'this.model.btSjgl==');
					this.$forceUpdate()
			},
			btJslmInput(e){
					if(e && this.model.btQslm){
						this.model.btSjgl =  e - this.model.btQslm
					}else{
						this.model.btSjgl = ''
					}
					console.log(this.model.btSjgl,'this.model.btSjgl==');
				this.$forceUpdate()
			},
			btRqConfirm(e) {
				//+ " " + e.hour+ ":" + e.minute + ":" + e.second
				this.model.btRq = e.year + "-" + e.month + "-" + e.day 
			},
			btScsjConfirm(e) {
				this.model.btScsj = e.year + "-" + e.month + "-" + e.day + " " + e.hour+ ":" + e.minute 
			},
			btXcsjConfirm(e) {
				this.model.btXcsj = e.year + "-" + e.month + "-" + e.day + " " + e.hour+ ":" + e.minute 
			},
			shangche(){
				uni.navigateTo({
					url: "/pages/index/form3?id=" + this.model.id,
				});
			},
			queren(){
				
				// if(this.model.btRq == null || this.model.btRq == ''){
				// 	this.$refs.jsError.showError('','请先选择日期！','error');
				// 	return;
				// }
				// if(this.model.btXslx == null || this.model.btXslx == ''){
				// 	this.$refs.jsError.showError('','请先输入行驶路线！','error');
				// 	return;
				// }
				// if(this.model.btQslm == null || this.model.btQslm == ''){
				// 	this.$refs.jsError.showError('','请先输入起始路码！','error');
				// 	return;
				// }
				// if(this.model.btJslm == null || this.model.btJslm == ''){
				// 	this.$refs.jsError.showError('','请先输入结束路码！','error');
				// 	return;
				// }
				// if(this.model.btScsj == null || this.model.btScsj == ''){
				// 	this.$refs.jsError.showError('','请先选择上车时间！','error');
				// 	return;
				// }
				// if(this.model.btXcsj == null || this.model.btXcsj == ''){
				// 	this.$refs.jsError.showError('','请先选择下车时间！','error');
				// 	return;
				// }
				
				// if (!this.model.dataMap || !this.model.dataMap.carddYcddrw_file_bt_qs) {
				// 	this.$u.toast("请上传起始路码照片");
				// 	return;
				// }
				// if (!this.model.dataMap || !this.model.dataMap.carddYcddrw_file_bt_js) {
				// 	this.$u.toast("请上传结束路码照片");
				// 	return;
				// }
				
				
				
				this.$u.api.car.carddYcddrwlsaveBt(this.model).then(res => {
					if(res.result == 'true'){
						this.$u.toast(res.message);
						setTimeout(()=>{
							uni.$emit('detailsData','')
							uni.navigateBack({
								delta: 1
							})
						},500)
					}else{
						this.$refs.jsError.showError('',res.message,'error');
					}
				});
				// uni.$emit('detailsData', this.model);
				// uni.navigateBack({
				// 	delta: 1,
				// })
			},
			quxiao(){
				uni.navigateBack({
					delta: 1
				})
			},
			showKeyboard(ref){
				this.$refs[ref].toShow(this.model.carNo)
			},
			timeConfirm(e){
				this.model.planDate = e.year + '-' + e.month + '-' + e.day +' '+e.hour +":"+e.minute +":"+e.second;
			},
			submit(data, callback) {
				if(this.model.carNo == null || this.model.carNo == ''){
					this.$refs.jsError.showError('','请正确填入车牌号！','error');
					return;
				}else if(this.model.planDate == null || this.model.planDate == ''){
					this.$refs.jsError.showError('','请正确选择计划发货日期！','error');
					return;
				}else if(this.model.carVenCode == null || this.model.carVenCode == ''){
					this.$refs.jsError.showError('','请正确选择运输单位！','error');
					return;
				}else if(this.model.ipicture == undefined || this.model.ipicture == ''){
					this.$refs.jsError.showError('','请必填图片是否可再次上传！','error');
					return;
				}else{
					var data = {
						mfCarplanFhH: JSON.stringify(this.model),
						useStatus: 1
					};
					this.$u.api.mffh.save(data).then(res => {
						if(res.result == 'true'){
							this.$u.toast(res.message);
							setTimeout(()=>{
								uni.$emit('refreshData');
								uni.navigateBack({
									delta: 1
								})
							},500)
						}else{
							this.$refs.jsError.showError('',res.message,'error');
						}
					});
				}
			},
		}
	}
</script>
<style scoped  lang="less">
.footer {
	position: fixed;
	left: 0;
	bottom: 0;
	width: 100%;
	background: #fff;z-index: 999;
	border-top: 1px solid #aaa;
}

.cu-bar {
		min-height: 60px;
	}

	.cu-btn {
		font-size: 16px;
		height: 40px;
	}

	.foot {
		background: #fff;
	}

	.cu-item {
		flex-wrap: wrap;
		padding: 8px 15px !important;
	}

	.title {
		width: 220rpx;
	}

	.cu-modal-slot {
		margin: 42rpx 32rpx 20rpx 32rpx;
		height: 100vh;
		position: relative;
	}

	.cu-modal-header {
		font-size: 38rpx;
		color: #3d3d3d;
		line-height: 40rpx;
		text-align: left;
		font-weight: 500;
	}

	.button {
		font-size: 32rpx;
		color: #666666;
		line-height: 40rpx;
		padding: 12rpx 40rpx;
		margin-bottom: 20rpx;
		background: #e2e2e2;
		border-radius: 180rpx;
	}

	.button:hover {
		background: #3e97b0;
		color: #ffffff;
	}

	.cu-modal-search-title {
		margin: 32rpx 0rpx 20rpx 0rpx;
		text-align: left;
		font-size: 32rpx;
		color: #999999;
		line-height: 40rpx;
	}

	.cu-dialog {
		background: #ffffff;
	}

	.cu-modal-footer {
		padding: 32rpx 32rpx;
		width: 100%;

		.cu-btn {
			width: 50%;
		}
	}

	.bg-confirm {
		background: #3e97b0;
		color: #ffffff;
	}
</style>