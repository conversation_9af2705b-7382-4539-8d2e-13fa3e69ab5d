<template>
	<view>
		
		<!-- 驾驶员 -->
		<indexJsy ref="indexJsy" v-if="vuex_user.extend.extendS2 == '02'" />
		<!-- 医院 -->
		<indexYy ref="indexYy" v-if="vuex_user.extend.extendS2 == '01'" />
		<!-- 其它 -->
		<indexQt ref="indexQt" v-if="vuex_user.extend.extendS2 == '09' || vuex_user.extend.extendS2 == '10' || !vuex_user.extend.extendS2" />
	</view>
</template>
<script>
	/**
	 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
	 */

	import indexYy from "@/pages/sys/home/<USER>";
	import indexJsy from "@/pages/sys/home/<USER>";
	import indexQt from "@/pages/sys/home/<USER>";

	export default {
		components: {
			indexJsy,
			indexYy,
			indexQt
		},
		data() {
			return {
				show: true
			};
		},
		onLoad() {},
		onShow() {
			this.$u.api.index({loginCheck: true}).then(res => {
				if (typeof res === 'object' && res.result == 'login'){
					this.$u.toast('登录超时！')
					uni.reLaunch({
						url: '/pages/sys/login/index'
					});
				}
			});
			
			if(this.vuex_user.extend.extendS2 == '01'){
				this.$refs.indexYy.getIsPre()
				this.$refs.indexYy.findNewRecord()
			}else if(this.vuex_user.extend.extendS2 == '02'){
				this.$refs.indexJsy.query.pageNo = 1
				this.$refs.indexJsy.loadData()
				this.$refs.indexJsy.getLTreeData()
			}else{
				this.$refs.indexQt.refreshCount()
			}
		},
		methods: {
			confirmqx() {},
			confirmok() {
			},

		}
	};
</script>
<style lang="scss">
	page {
		// background-color: #f8f8f8;
		background-color: #e6e6e6;
	}
</style>