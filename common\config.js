/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 */
const config = {
	
	// 产品名称
	productName: 'e企派车',
	
	// 公司名称
	companyName: '重庆轻企信息技术有限公司',
	
	// 产品版本号
	productVersion: 'V4.3.5',
	
	// 版本检查标识
	appCode: 'android',
	
	// 内部版本号码
	appVersion: 1,
	
	// 管理基础路径
	adminPath: '/a',
	
	
	// 是否可预约
	reservation:false
	
}

config.baseUrlList = [
	
	{
		name: 'https://cqqingqi.com:3200',
		baseUrl: 'https://cqqingqi.com:3200/car',
		value: '0'
	},
	// {
	// 	name: 'http://192.168.1.114:8980',
	// 	baseUrl: 'http://192.168.1.114:8980/car',
	// 	value: '0'
	// },
	
	// {
	// 	name: 'https://m8.cqsxyf.com:8980',
	// 	baseUrl: 'https://m8.cqsxyf.com:8980/SXYF',
	// 	value: '0'
	// },
	// {
	// 	name: 'https://m8.cqbsklkj.com:8980',
	// 	baseUrl: 'https://m8.cqbsklkj.com:8980/SXYF',
	// 	value: '1'
	// },
	{
		name: 'https://demo.jeesite.com',
		baseUrl: 'https://demo.jeesite.com/js',
		value: '1'
	},{
		name: 'http://192.168.0.11:8980',
		baseUrl: 'http://192.168.0.11:8980/js',
		value: '2'
	},{
		name: 'http://127.0.0.1:8980',
		baseUrl: 'http://127.0.0.1:8980/js',
		value: '3'
	}
];

// 设置后台接口服务的基础地址
config.baseUrl = config.baseUrlList[0].baseUrl;

// 建议：打开下面注释，方便根据环境，自动设定服务地址
if (process.env.NODE_ENV === 'development'){
	// config.baseUrl = '/../js'; // 代理模式 vue.config.js 中找到 devServer 设置的地址
	// config.baseUrl = 'http://127.0.0.1:8980/js';
}

export default config;