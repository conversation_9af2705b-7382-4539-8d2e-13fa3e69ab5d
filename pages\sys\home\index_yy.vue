<template>
	<view class="wrap" style="display: flex;height: 100vh;flex-direction: column;">
		<!-- 医院 -->
		<!-- <js-lang title="home.title" :showBtn="true"></js-lang> -->
		<!-- <u-sticky> -->
		<view style="padding: 10px; background: #fff;margin-bottom: 10px;">
			<view style="text-align: center;font-weight: bold;">
				{{vuex_user.extend.extendS3?vuex_user.extend.extendS3:'暂无绑定医院'}}
			</view>
		</view>
		<!-- </u-sticky> -->



		<!-- <u-swiper :height="400" :list="imgList" :title="false" @click="imgListClick"></u-swiper> -->

		<view style="flex: 1;overflow: hidden;">
			<view
				style="width: 100%;height: 100%;background: #fff;display: flex;justify-content: center;align-items: center;">
				<view v-if="zsFlag == '1'"
					style="display: flex;justify-content: center;align-items: center; flex-direction: column;">
					<u-icon name="checkmark-circle-fill" color="#55aa00" size="110"></u-icon>
					<view style="font-weight: bold;margin-bottom: 40rpx;font-size: 18px;">预约成功</view>
					<view style="text-align: center;margin-bottom: 20px;" v-if="data.planDate">
						计划收运时间：{{data.planDate}}
					</view>
					<view style="font-size: 12px;color: #aaa;" @click="qxShow = true"> <text style="color: red;text-decoration: underline;"> 取消预约 </text></view>
				</view>
				
				<view v-if="zsFlag == '2'"
					style="display: flex;justify-content: center;align-items: center; flex-direction: column;position: relative;">

					<image @click="fqyy" src="/static/jeesite/img/ljyy.gif" mode="aspectFit" />
					<view style="position: absolute;bottom:50px">
						<text v-if="vuex_config.reservation">
							点击上方预约按钮即可完成预约！
						</text>
						<text v-else>
							应监管局要求，将自动预约
						</text>
					</view>
					
					
				</view>
			</view>
		</view>

		<view>
			<view style="padding: 10px;" v-if="shData.id">
				<u-section title="待确定收运清单" :right="false" line-color="#00aa00" font-size="32"></u-section>
			</view>

			<!-- 无预约展示 -->
			<!-- <view style="padding: 20px 10px;" v-if="!zsFlag">
				<u-empty  text="暂无待确定收运清单!" mode="order"></u-empty>
			</view> -->
			<view style="padding: 0 10px;" v-if="shData.id">
				<view style="padding: 10px;border-radius: 10px;background: #fff;position: relative;">
					<view style="margin-bottom: 5px; display: flex">
							<text style="width: 90px;font-weight: bold;">驾驶员：</text>
							<text style="flex: 1;font-weight: bold;"> {{shData.driverName?shData.driverName:''}} </text>
					</view>
					<view style="margin-bottom: 5px; display: flex"><text style="width: 90px;">押运员：</text>
						<text style="flex: 1;"> {{shData.driver2Name?shData.driver2Name:''}} </text>
					</view>
					<view style="margin-bottom: 5px; display: flex"><text style="width: 90px;">车牌号：</text>
						<text style="flex: 1;"> {{shData.carNo?shData.carNo:''}} </text>
					</view>
					<view style="margin-bottom: 5px; display: flex"><text style="width: 90px;">感染数(箱)：</text>
						<text style="flex: 1;"> {{shData.qtyGr}} </text>
					</view>
					<view style="margin-bottom: 5px; display: flex"><text style="width: 90px;">损伤数(箱)：</text>
						<text style="flex: 1;"> {{shData.qtyPs}} </text>
					</view>
					<view style="margin-bottom: 5px; display: flex"><text style="width: 90px;">总数(箱)：</text>
						<text style="flex: 1;color: #ff0000;"> {{shData.qtySum}} </text>
					</view>
					<!-- color: #afaf00; -->
					<view style="margin-bottom: 5px; display: flex"><text style="width: 90px;">感染性(kg)：</text>
						<text style="flex: 1;"> {{shData.weightGr}} </text>
					</view>
					<view style="margin-bottom: 5px; display: flex"><text style="width: 90px;">损伤性(kg)：</text>
						<text style="flex: 1;"> {{shData.weightPs}} </text>
					</view>
					<view style="margin-bottom: 5px; display: flex"><text style="width: 90px;">总量(kg)：</text>
						<text style="flex: 1;color: #ff0000;"> {{shData.weightSum}} </text>
					</view>

					<view @click="yyShow = true" style="color: #fff; position: absolute;right: 10px;bottom: 10px;
					padding: 10px;border: 1px solid #00aa00;border-radius: 10px;background: #00aa00;">
						确定收运
					</view>
				</view>
			</view>


			<!-- title="确定预约" -->
			<u-modal v-model="yyShow" :mask-close-able="true" :show-cancel-button="true"
				@confirm="confirmok">

				<view style="padding: 10px;text-align: center;">
					<!-- <textarea style="background-color: #f7f7f7;padding: 10px;" v-model="yyData.receiveRemarks"
						type="text" placeholder="请输入预约备注" /> -->
						是否确定已经收运？
				</view>
			</u-modal>

			<!-- title="取消预约" -->
			<u-modal v-model="qxShow" :mask-close-able="true" :show-cancel-button="true"
				@confirm="confirmqx">

				<view style="padding: 10px;text-align: center;">
					<!-- <textarea style="background-color: #f7f7f7;padding: 10px;" v-model="yyData.receiveRemarks"
						type="text" placeholder="请输入取消原因" /> -->
						是否确定取消预约？
				</view>
			</u-modal>

		</view>


		<view style="height: 10px;">

		</view>
	</view>
</template>
<script>
	export default {
		options: {
			styleIsolation: 'shared'
		},
		data() {
			return {
				tzshow: false,
				yyData: '',
				yyShow: false,
				qxShow: false,
				zsFlag: '',
				shData: {
					id:''
				},
				data:{},
				imgList: [
					// {image: '/static/jeesite/banner/1.svg'},
					// {image: '/static/jeesite/banner/2.svg'},
					// {image: '/static/jeesite/banner/3.svg'},
					{
						image: '/static/jeesite/banner/1.png'
					},
					// {image: '/static/jeesite/banner/2.png'},
					{
						image: '/static/jeesite/banner/3.png'
					},
					{
						image: '/static/jeesite/banner/4.png'
					},
					{
						image: '/static/jeesite/banner/5.png'
					},
				],

				todoCount: 0,



			};
		},
		onLoad() {
		},
		onShow() {
		},
		methods: {
			// 获取待收运确定
			findNewRecord(){
				this.$u.api.yysh.findNewRecord().then(res => {
					this.shData = res.data?res.data:{}
				})
			},
			
			// 获取预约状态
			getIsPre() {
				this.$u.api.yysh.getIsPre().then(res => {
					this.data = res.data
					this.zsFlag = res.result == "true" ? '1' : '2'
				})
			},
			// 预约
			fqyy() {
				if(this.vuex_config.reservation){
					this.$u.api.yysh.yfPreRecordSave({
						// code: this.logincode,
						// userCode: this.vuex_user.userCode,
					}).then(res => {
						if (res.result == "true") {
							this.$u.toast(res.message);
							this.getIsPre()
						}else {
						  uni.showModal({
							title: "提示",
							content: res.message,
							showCancel: false,
							success: (res) => {},
						  });
						  // this.$refs.jsError.showError("", res.message, "error");
						}
					})
				}else{
					uni.showModal({
						title: "提示",
						content: '应监管局要求，将自动预约',
						showCancel: false,
						success: (res) => {},
					});
				}
				
			},
			// 取消预约
			confirmqx() {
				this.$u.api.yysh.yfPreRecordDelete().then(res => {
					
					if (res.result == "true") {
						this.$u.toast(res.message);
						this.getIsPre()
					}else {
					  uni.showModal({
						title: "提示",
						content: res.message,
						showCancel: false,
						success: (res) => {},
					  });
					  // this.$refs.jsError.showError("", res.message, "error");
					}
				})
			},
			confirmok() {
				// this.zsFlag = !this.zsFlag
				this.$u.api.yysh.confirm({id:this.shData.id}).then(res => {
					this.$u.toast(res.message);
					if (res.result == "true") {
						this.getIsPre()
						this.findNewRecord()
					}
				})
				
			},
			navTo(url) {
				uni.navigateTo({
					url: url
				});
			},
			
			imgListClick(index) {
				console.log(`点击了第${index + 1}页图片`)

				// let urls = this.imgList.map(val => {
				// 	// return (this.baseUrl +  val.image)
				// 	return  val.image
				// })
				// console.log(urls);
				// uni.previewImage({
				// 	urls, //需要预览的图片http链接列表，多张的时候，url直接写在后面就行了
				// 	current: this.imgList[index].image, // 当前显示图片的http链接，默认是第一个
				// 	success: function(res) {},
				// 	fail: function(res) {},
				// 	complete: function(res) {},
				// })
			},
			
		}
	};
</script>
<style lang="scss" scoped>
	@import 'index.scss';

	page {
		// background-color: #f8f8f8;
		background-color: #e6e6e6;
	}

	// /deep/.u-th .data-v-070b0340 {
	// 	height: 100% !important;
	// }

	.u-grid.data-v-a7b3bc80 {
		width: 100%;
		position: relative;
		box-sizing: border-box;
		overflow: hidden;
		border-radius: 10px;
	}

	.cu-bar {
		min-height: 80px;
	}

	.cu-btn {
		font-size: 16px;
		height: 40px;
	}

	.foot {
		background: #fff;
	}

	.cu-item {
		flex-wrap: wrap;
		padding: 15px !important;
	}
</style>