<template>
	<view class="wrap">
		<js-error mode="bottom" ref="jsError"></js-error>
	
		<u-form class="form bg-white" :model="model"  ref="uForm" label-position="left" style="margin-top: -20px;">
			<view style="display: flex;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="车牌号码:" prop="cphm" label-width="150" >
					<u-input placeholder=" " v-model="model.cphm" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="车辆类型:" prop="cllx" label-width="150" >
					<js-select v-model="model.cllx" :disabled="true" dict-type="car_type" placeholder=" "></js-select>
				</u-form-item>
			</view>
			
			<view style="display: flex;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="品牌型号:" prop="ppxh" label-width="150" >
					<u-input placeholder=" " v-model="model.ppxh" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="车辆状态:" prop="clzt" label-width="150" >
					<js-select v-model="model.clzt" :disabled="true" dict-type="car_status" placeholder=" "></js-select>
				</u-form-item>
			</view>
			
			<view style="display: flex;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="座位数:" prop="zws" label-width="150" >
					<u-input placeholder=" " v-model="model.zws" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="使用性质:" prop="syxz" label-width="150" >
					<js-select v-model="model.syxz" :disabled="true" dict-type="car_use_type" placeholder=" "></js-select>
				</u-form-item>
			</view>
			<u-form-item  label="车辆识别号:" prop="clsbdh" label-width="170" >
				<u-input placeholder=" " v-model="model.clsbdh" type="text" :disabled="true" di maxlength="64"></u-input>
			</u-form-item>
			
			<view style="display: flex;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="限行标志:" prop="xxbz" label-width="150" >
					<u-input placeholder=" " v-model="model.xxbz" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="发动机号码:" prop="fdjhm" label-width="170" >
					<u-input placeholder=" " v-model="model.fdjhm" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
				
			</view>
			<view style="display: flex;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="车体颜色:" prop="ctys" label-width="150" >
					<u-input placeholder=" " v-model="model.ctys" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="新能源汽车:" prop="xnyqc" label-width="170" >
					<js-select v-model="model.xnyqc" :disabled="true" dict-type="sys_yes_no" placeholder=" "></js-select>
				</u-form-item>
			</view>
			
			<view style="display: flex;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="车辆配置:" prop="clpz" label-width="150" >
					<u-input placeholder=" " v-model="model.clpz" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="核定载人数:" prop="hdzrs" label-width="170" >
					<u-input placeholder=" " v-model="model.hdzrs" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
			</view>
			<view style="display: flex;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="年检日期:" prop="njrq" label-width="150" >
					<u-input placeholder=" " v-model="model.njrq" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="保险到期:" prop="bxdq" label-width="150" >
					<u-input placeholder=" " v-model="model.bxdq" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
			</view>
			
			<u-form-item style="flex: 1;" label="图片:" prop="dataMap" label-width="170" >
				<js-uploadfile  v-model="model.dataMap" maxCount="0" imageMaxWidth="-1" imageMaxHeight="-1" :biz-key="model.id" biz-type="basCar_image"></js-uploadfile>
			</u-form-item>
			
			
		</u-form>
	
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// value: 
				model:{
					// id:'',
					// carNo:'',
					// planDate:'',
					// cdriver:'',
					// driverPhone:'',
					// carVenCode:'',
					// carVenName:'',
					// remarks:'',
				},
				carVenSelectList: [],
				ycddrwDyrqTime:false,
				params: {
					year: true,
					month: true,
					day: true,
					// hour: true,
					// minute: true,
					// second: true
				},
				children:[],
				pickerTime: false,//控制日期显示
			}
		},
		onLoad(params){
			// this.model = JSON.parse(params.model)
			this.model = params
			// this.$u.api.car.carddYcddrwlform({id:params.id}).then(res => {
			// 	this.model = res.carddYcddrw
				
			// 	this.children = res.carddYcddrw.carddYcddrwBtList
			// });
			
			// uni.$on('detailsData', (data)=>{
			// 	console.log(data);
			// 	if(data.index || data.index==0){
			// 		this.children[data.index] = data
			// 		this.$forceUpdate()
			// 	}else{
			// 		this.children.push(data)
			// 	}
				
			// });
			
		},
		onShow() {
			// this.children =  []
			this.$u.api.car.basCarform({id:this.model.id}).then(res => {
				this.model = res.basCar
			});
		},
		onReady() {
			// // 运输单位数据
			// this.$u.api.carVen.treeData().then(res => {
			// 	this.carVenSelectList = res;
			// });
		},
		methods: {
			delDetail(item, index) {
				let that = this
				uni.showModal({
					title: '删除提示',
					content: '是否删除此货位信息？',
					confirmColor: '#F54E40',
					success: (res) => {
						if (!res.confirm) {
							return false;
						}
			
						// that.children.splice(index, 1);
						// item.status = '1'
						this.$u.api.car.carddYcddrwldeleteBt({id:item.id}).then(res => {
							if(res.result == 'true'){
								this.$u.toast(res.message);
								this.children.splice(index, 1);
							}else{
								this.$refs.jsError.showError('',res.message,'error');
							}
						})
						that.$forceUpdate()
					}
				})
			},
			handleSubmit(ope, oldStatus, newStatus){
				let data = {
					...this.model,
					ope,
					oldStatus,
					newStatus
				}
				data.carddYcddrwBtList = this.children
				this.$u.api.car.carddYcddrwlsave(data).then(res => {
					if(res.result == 'true'){
						this.$u.toast(res.message);
						setTimeout(()=>{
							uni.navigateBack({
								delta: 1
							})
						},500)
					}else{
						this.$refs.jsError.showError('',res.message,'error');
					}
				})
			},
			ycddrwDyrqConfirm(e) {
				//+ " " + e.hour+ ":" + e.minute + ":" + e.second
				this.model.ycddrwDyrq = e.year + "-" + e.month + "-" + e.day 
				console.log(this.model,'44455566');
			},
			edit(item,index){
				// item.index = index
				// item.['parentId.id'] = this.model.id
				let obj = {
					...item,
					'parentId.id':this.model.id
				}
				uni.navigateTo({
					url: "/pages/index/detailsForm2?item=" + JSON.stringify(obj),
				});
			},
			GoaddChild(){
				let obj = {
					parentId:{
						id:this.model.id,
					}
				}
				uni.navigateTo({
					url: "/pages/index/detailsForm2?item=" + JSON.stringify(obj),
				});
			},
			renwu(){},
			chejian(){
				// uni.navigateTo({
				// 	url: "/pages/index/form2?id=" + this.model.id,
				// });
			},
			baocun(){},
			quxiao(){
				uni.navigateBack({
					delta: 1
				})
			},
			showKeyboard(ref){
				this.$refs[ref].toShow(this.model.carNo)
			},
			timeConfirm(e){
				this.model.planDate = e.year + '-' + e.month + '-' + e.day +' '+e.hour +":"+e.minute +":"+e.second;
			},
			submit(data, callback) {
				if(this.model.carNo == null || this.model.carNo == ''){
					this.$refs.jsError.showError('','请正确填入车牌号！','error');
					return;
				}else if(this.model.planDate == null || this.model.planDate == ''){
					this.$refs.jsError.showError('','请正确选择计划发货日期！','error');
					return;
				}else if(this.model.carVenCode == null || this.model.carVenCode == ''){
					this.$refs.jsError.showError('','请正确选择运输单位！','error');
					return;
				}else if(this.model.ipicture == undefined || this.model.ipicture == ''){
					this.$refs.jsError.showError('','请必填图片是否可再次上传！','error');
					return;
				}else{
					var data = {
						mfCarplanFhH: JSON.stringify(this.model),
						useStatus: 1
					};
					this.$u.api.mffh.save(data).then(res => {
						if(res.result == 'true'){
							this.$u.toast(res.message);
							setTimeout(()=>{
								uni.$emit('refreshData');
								uni.navigateBack({
									delta: 1
								})
							},500)
						}else{
							this.$refs.jsError.showError('',res.message,'error');
						}
					});
				}
			},
		}
	}
</script>
<style scoped  lang="less">
	
.footer {
	position: fixed;
	left: 0;
	bottom: 0;
	width: 100%;
	background: #fff;z-index: 999;
	border-top: 1px solid #aaa;
}

.cu-bar {
		min-height: 60px;
	}

	.cu-btn {
		font-size: 16px;
		height: 40px;
	}

	.foot {
		background: #fff;
	}

	.cu-item {
		flex-wrap: wrap;
		padding: 8px 15px !important;
	}

	.title {
		width: 220rpx;
	}

	.cu-modal-slot {
		margin: 42rpx 32rpx 20rpx 32rpx;
		height: 100vh;
		position: relative;
	}

	.cu-modal-header {
		font-size: 38rpx;
		color: #3d3d3d;
		line-height: 40rpx;
		text-align: left;
		font-weight: 500;
	}

	.button {
		font-size: 32rpx;
		color: #666666;
		line-height: 40rpx;
		padding: 12rpx 40rpx;
		margin-bottom: 20rpx;
		background: #e2e2e2;
		border-radius: 180rpx;
	}

	.button:hover {
		background: #3e97b0;
		color: #ffffff;
	}

	.cu-modal-search-title {
		margin: 32rpx 0rpx 20rpx 0rpx;
		text-align: left;
		font-size: 32rpx;
		color: #999999;
		line-height: 40rpx;
	}

	.cu-dialog {
		background: #ffffff;
	}

	.cu-modal-footer {
		padding: 32rpx 32rpx;
		width: 100%;

		.cu-btn {
			width: 50%;
		}
	}

	.bg-confirm {
		background: #3e97b0;
		color: #ffffff;
	}
</style>