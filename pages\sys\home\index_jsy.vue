<template>
	<view class="container">
		<js-error mode="bottom" ref="jsError"></js-error>

		<!-- 驾驶员 -->
		<u-sticky>
			<view
				style="padding: 10px;background: #fff;display: flex;justify-content: space-between;align-items: center;">
				<view style="display: flex;align-items: center;">
					<!-- 收运登记 -->
					<u-section title="待收运清单" :right="false" line-color="#00aa00" font-size="32"></u-section>
					<!-- <u-icon @click="navTo('/pages/history/list')" class="grid-icon" name="order" :size="70" :style="{ color: '#00aa00','margin-left':'10px' }"></u-icon> -->
				</view>

				<view class="">
					<!-- quhuo({})  -->
					<u-icon @click="quhuo2()" class="grid-icon" name="/static/jeesite/img/tj.png" :size="80"
						:style="{ color: '#00aa00' }"></u-icon>
				</view>


			</view>
		</u-sticky>
		<view class="" style="position: absolute;left: 2000px;">
			<js-select ref="inputRef" id="hosp" :childName="'None'" :showFilter="true" v-model="model.hospCode"
				:items="selectList" placeholder="请选择" :tree="false" :label-value="model.hospName" :flag="true"
				@label-input="model.hospName = $event" @confirm="selectConfirm">
			</js-select>
		</view>

		<!-- <view class="search">
			<u-search @click="show = true" :show-action="false" placeholder="搜索" :disabled="true"></u-search>
		</view> -->

		<scroll-view class="scroll-list" scroll-y="true" @scrolltolower="loadMore" :refresher-enabled="true"
			:refresher-triggered="triggered" @refresherrefresh="refresherrefresh" style="background-color: #e6e6e6;">
			<!-- 判断warehouseTypeNormal为0时的点击事件 -->
			<view v-if="list.length" class="tolView" v-for="(item, index) in list" :key="item.id"
				style="position: relative;">
				<!-- <view class="text" style="color: #000;display: flex;justify-content: space-between;margin-bottom: 5px;">
					<view style="margin: auto 0;">
						<view style=" display: inline-block;padding: 5px;background: #00aa00;color: #fff;width: 40px;text-align: center;font-style: italic;font-weight: bold">
							{{ index + 1 }}
						</view>
						<text class="text-bold" style="font-size: 30rpx;font-weight: bold;margin-left: 10px;">{{item.hospName}}</text>
					</view>
				
				</view> -->
				<view style="margin-bottom: 5px; display: flex;font-size: 30rpx;">
					<view style="width: 80px;">
						<view
							style=" display: inline-block;padding: 5px;background: #00aaff;color: #fff;width: 40px;text-align: center;font-style: italic;font-weight: bold">
							{{ index + 1 }}
						</view>
					</view>
					<text class="text-bold" style="flex: 1;font-size: 30rpx;font-weight: bold;">{{item.hospName}}</text>
					<!-- <text style="flex: 1;"> {{item.createDate}} </text> -->
				</view>
				<view style="margin-bottom: 5px; display: flex;font-size: 30rpx;"><text
						style="width: 80px;">预约时间：</text>
					<text style="flex: 1;"> {{item.createDate}} </text>
				</view>

				<view style="margin-bottom: 5px; display: flex"><text style="width: 80px;">地址：</text>
					<text style="flex: 1;"> {{ item.m8ViewHosp && item.m8ViewHosp.addr?item.m8ViewHosp.addr:'' }}</text>
				</view>
				<view style="margin-bottom: 5px; display: flex"><text style="width: 80px;">线路：</text>
					<text style="flex: 1;"> {{ item.m8ViewHosp?item.m8ViewHosp.croute:'' }} </text>
				</view>
				<view style="margin-bottom: 5px; display: flex"><text style="width: 80px;">线路顺序：</text>
					<text style="flex: 1;"> {{ item.m8ViewHosp?item.m8ViewHosp.routeSort:'' }} </text>
				</view>
				<view style="margin-bottom: 5px; display: flex"><text style="width: 80px;">电话：</text>
					<text style="flex: 1;"> {{ item.creator?item.creator.mobile:'' }}</text>
					<u-icon name="phone" size="60" color="#6169ff" @click="callNumber(item.creator.mobile)"></u-icon>
					<!-- <text style="flex: 1;" @click="callNumber(item.creator.mobile)"> {{ item.creator?item.creator.mobile:'' }}</text> -->
				</view>
				<!-- <view  @click="quhuo(item)" style="color: #fff; position: absolute;right: 10px;bottom: 10px;
				padding: 10px;border: 1px solid #2979ff;border-radius: 10px;background: #2979ff;">
					确定收运
				</view> -->
				<view style="border-top: 1px #aaa solid;height: 1px;margin: 10px 0;">

				</view>
				<view style="display: flex;justify-content: space-between;font-size: 40rpx;">
					<view @click="jl(item)" style="color: #aaa; display: inline-block;margin-right: 10px;
					padding: 10px;border: 1px solid #aaa;">
						<u-icon name="close" size="40" color="red"></u-icon> 收运失败
					</view>

					<view @click="quhuo(item)" style="color: #000;display: inline-block;
					padding: 10px;border: 1px solid #00aa00;">
						<u-icon name="checkmark" size="40" color="#00aa00"></u-icon> 收运成功
					</view>
				</view>




			</view>
			<view v-if="list.length" class="loadmore">
				<u-loadmore :status="loadStatus"></u-loadmore>
			</view>
			<u-empty v-if="!list.length" text="暂无待收运清单!" mode="data"></u-empty>
		</scroll-view>

		<u-modal v-model="hospShow" :mask-close-able="true" :show-cancel-button="true" :show-title="false"
			@confirm="modalConfirm">
			<view style="text-align: center;padding: 20px 30px 10px;">
				<view style="margin-bottom: 10px;">
					收运登记选择
				</view>
				<!-- <js-select  :childName="'None'" :showFilter="true" v-model="model.hospCode" :items="selectList"
					placeholder="请选择" :tree="false" :label-value="model.hospName" :flag="true"
					@label-input="model.hospName = $event">
					
				</js-select> -->
				<view style="display: flex;justify-content: space-between;flex: 1;margin: 10px 0;">
					<view @click="ctype(2)" :class="model.ctype == 2 ? 'active1': ''"
						style="border: 1px solid #aaa;padding: 16rpx 20rpx ;color: #aaa;">
						<u-icon name="close" size="30" ></u-icon>收运失败</view>
					<view @click="ctype(1)" :class="model.ctype == 1 ? 'active': ''"
						style="border: 1px solid #aaa;padding: 16rpx 20rpx ;color: #aaa;">
						<u-icon name="checkmark" size="30" ></u-icon>收运成功</view>
				</view>
			</view>
		</u-modal>

	</view>
</template>

<script>
	import dictLabel from "@/components/dictLabel.vue";
	export default {
		components: {
			dictLabel,
		},
		data() {
			return {
				itemData:{},
				hospShow: false,
				model: {
					ctype:1
				},
				selectList: [],
				triggered: false,
				startTime: false, //控制日期显示
				show: false,
				smshow: false,
				focus: true,
				barCode: "",
				list: [],
				query: {
					pageNo: 1,
					pageSize: 20,
				},
				loadStatus: "loadmore",
				x: 650, //x坐标
				y: 650, //y坐标
				TASK_BUS_TYPE: "0", //任务类型
			};
		},
		onLoad() {},
		onShow() {},
		methods: {
			// makeCall(phone) {
			//  console.log(1111);
			//      uni.getSetting({
			//        success: (res) => {
			// 		console.log(res);
			//          if (res.authSetting['scope.makePhoneCall']) {
			//            this.callNumber(phone);
			//          } else {
			//            uni.authorize({
			//              scope: 'scope.makePhoneCall',
			//              success: () => {
			//                this.callNumber(phone);
			//              },
			//              fail: (e) => {
			// 			  console.log(e,'fail==');
			//                uni.openSetting({
			//                  success: (res) => {
			// 				  console.log(res,'55555');
			//                    if (res.authSetting['scope.makePhoneCall']) {
			//                      this.callNumber(phone);
			//                    }
			//                  }
			//                });
			//              }
			//            });
			//          }
			//        }
			//      });
			//    },
		   async selectConfirm() {
				this.$u.api.yysh.getPreByHosp({
					hospCode: this.model.hospCode || ''
				}).then((res) => {
					if (res.result == "true") {
						console.log(res.data);
						this.model.ctype = 1
						this.hospShow = true
						this.itemData = res.data
						// uni.navigateTo({
						// 	url: "/pages/sys/home/<USER>",
						// 	success: function(res1) {
						// 		// 通过eventChannel向被打开页面传送数据
						// 		// 其中含有两个参数，第一个是接收的函数名，第二个则是需要携带的参数
						// 		res1.eventChannel.emit('acceptDataFrom', res.data)
						// 	}
						// });
					}
				});
			},
			modalConfirm(){
				let that = this
				if(this.model.ctype == 1){
					uni.navigateTo({
						url: "/pages/sys/home/<USER>",
						success: function(res1) {
							// 通过eventChannel向被打开页面传送数据
							// 其中含有两个参数，第一个是接收的函数名，第二个则是需要携带的参数
							res1.eventChannel.emit('acceptDataFrom', that.itemData)
						}
					});
				}else{
					uni.navigateTo({
						url: "/pages/sys/home/<USER>",
						success: function(res) {
							// 通过eventChannel向被打开页面传送数据
							// 其中含有两个参数，第一个是接收的函数名，第二个则是需要携带的参数
							res.eventChannel.emit('acceptDatajlFrom', that.itemData)
						}
					});
				}
			},
			getLTreeData() {
				this.$u.api.yysh.m8ViewHospTreeData({
					isShowCode: false,
					bused: 1
				}).then(res => {
					this.selectList = res.map(item => {
						item.value = item.id
						return item
					})
				})

			},
			callNumber(phone) {
				let that = this
				uni.makePhoneCall({
					phoneNumber: phone,
					success: () => {
						console.log('拨打电话成功！');
					},
					fail: () => {
						that.$u.toast('拨打电话失败！');
						// console.error('拨打电话失败！');
					}
				});
			},

			navTo(url) {
				uni.navigateTo({
					url: url
				});
			},
			startConfirm(e) {
				this.query.ddate = e.year + "-" + e.month + "-" + e.day;
			},
			reset() {
				this.query = {
					pageNo: 1,
					pageSize: 20,
				};
				this.loadData();
			},
			jl(item) {
				uni.navigateTo({
					url: "/pages/sys/home/<USER>",
					success: function(res) {
						// 通过eventChannel向被打开页面传送数据
						// 其中含有两个参数，第一个是接收的函数名，第二个则是需要携带的参数
						res.eventChannel.emit('acceptDatajlFrom', item)
					}
				});
			},
			quhuo2() {
				console.log(this.$refs.inputRef);
				// console.log(this.$el.querySelector('#hosp'));
				// this.$refs.hosp.click()
				this.$refs.inputRef.inputClick()

			},
			quhuo(item) {
				uni.navigateTo({
					url: "/pages/sys/home/<USER>",
					success: function(res) {
						// 通过eventChannel向被打开页面传送数据
						// 其中含有两个参数，第一个是接收的函数名，第二个则是需要携带的参数
						res.eventChannel.emit('acceptDataFrom', item)
					}
				});
			},
			loadMore() {
				this.loadStatus = "loading";
				setTimeout(() => {
					this.query.pageNo += 1;
					this.loadData("add");
				}, 100);
			},
			async refresherrefresh() {
				this.triggered = true;
				this.query.pageNo = 1;
				await this.loadData();
				setTimeout(() => {
					this.triggered = false;
				}, 500);
				// this.triggered = false;
			},
			loadData(type) {
				this.$u.api.yysh.listDriverData(this.query).then((res) => {
					this.show = false;
					if (res.list.length < 20 || res.list.length == 0) {
						this.loadStatus = "nomore";
					}
					var data = res.list;
					if (type == "add") {
						for (var i = 0; i < data.length; i++) {
							this.list.push(data[i]);
						}
					} else {
						this.list = data;
						// this.list = [{}];
					}

				});
			},
			ctype(ctype) {
				this.model.ctype = ctype
			},
			submit() {
				setTimeout(() => {
					this.query.pageNo = 1;
					this.loadData();
				}, 100);
			},
		},
	};
</script>

<style lang="scss">
	$all_width: 96rpx;
	$all_height: 96rpx;
	
	.active {
		border: 1px #00aa00 solid !important;
		color: #00aa00 !important;
	}
	.active1 {
		border: 1px red solid !important;
		color: red !important;
	}
	

	.movable-area2 {
		z-index: 999;
		height: 97vh;
		width: 650rpx;
		position: fixed;
		right: $all_width;
		top: -($all_height + 220rpx);
		pointer-events: none; //此处要加，鼠标事件可以渗透

		.movable-view {
			width: $all_width;
			height: $all_height;
			pointer-events: auto; //恢复鼠标事件

			image {
				width: $all_width;
				height: $all_height;
			}
		}
	}

	.movable-area1 {
		z-index: 999;
		height: 97vh;
		width: 650rpx;
		position: fixed;
		right: $all_width;
		top: -($all_height + 70rpx);
		pointer-events: none; //此处要加，鼠标事件可以渗透

		.movable-view {
			width: $all_width;
			height: $all_height;
			pointer-events: auto; //恢复鼠标事件

			image {
				width: $all_width;
				height: $all_height;
			}
		}
	}

	.movable-area {
		z-index: 888;
		height: 97vh;
		width: 650rpx;
		// max-width: 1152rpx;
		// 固定在右边
		top: -20rpx;
		position: fixed;
		right: $all_width;
		// 固定在右下角
		pointer-events: none; //此处要加，鼠标事件可以渗透

		.movable-view {
			width: $all_width;
			height: $all_height;
			pointer-events: auto; //恢复鼠标事件

			image {
				width: $all_width;
				height: $all_height;
			}
		}
	}

	.tableclass {
		border-left: 1px solid rgb(228, 231, 237);
		border-top: 1px solid rgb(228, 231, 237);
		background-color: rgb(255, 255, 255);
		width: 100%;
		box-sizing: border-box;
	}

	.thclass {
		text-align: center;
		padding: 5px 3px;
		border-bottom: 1px solid rgb(228, 231, 237);
		border-right: 1px solid rgb(228, 231, 237);
		flex-direction: column;
		flex: 1;
		justify-content: center;
		font-size: 14px;
		color: #303133;
		font-weight: bold;
		background-color: #f5f6f8;
	}

	.trclass {
		height: 52px;
	}

	.tdclass {
		padding: 5px 3px;
		border-bottom: 1px solid rgb(228, 231, 237);
		border-right: 1px solid rgb(228, 231, 237);
		flex-direction: column;
		flex: 1;
		justify-content: center;
		font-size: 14px;
		color: #505256;
		align-self: stretch;
		box-sizing: border-box;
		height: 100%;
	}

	.box .item {
		margin: 0 0px 0px;
	}

	.tui-line-cell {
		width: 100%;
		box-sizing: border-box;
		display: flex;
		align-items: center;
	}

	.tui-title {
		line-height: 32rpx;
		min-width: 120rpx;
		flex-shrink: 0;
	}

	.tui-input {
		font-size: 32rpx;
		color: #333;
		padding-left: 20rpx;
		flex: 1;
		overflow: visible;
	}

	.footer {
		position: fixed;
		left: 0;
		bottom: 20px;
		width: 100%;
	}

	.edit {
		position: absolute;
		right: 195rpx;
		top: 0;
		margin-right: 0;
	}

	.add {
		position: absolute;
		right: 130rpx;
		top: 0;
		margin-right: 0;
	}

	.upload {
		position: absolute;
		right: 65rpx;
		top: 0;
		margin-right: 0;
	}

	.rights {
		position: absolute;
		/* display: inline-block; */
		right: 0;
		top: 0;
		margin-right: 0;
		/* width: 100%; */
	}

	.addPlan {
		position: absolute;
		right: 60rpx;
		top: -40rpx;
	}

	.tolView {
		background-color: #fff;
		margin: 10px;
		padding: 10px;
		border-radius: 10px;
	}

	.checkRenWu {
		background: #014e3c;
		color: #fff;
	}
</style>