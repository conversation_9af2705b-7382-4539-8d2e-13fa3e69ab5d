<template>
	<view class="wrap">
		<js-error mode="bottom" ref="jsError"></js-error>

		<u-form class="form bg-white" :model="model" ref="uForm" label-position="left" style="margin-top: -20px;">

			<u-form-item label="订单编号:" prop="ycddDdbh" label-width="150">
				<u-input placeholder=" " v-model="model.ycddDdbh" type="text" :disabled="true" di
					maxlength="64"></u-input>
			</u-form-item>

			<!-- <view style="display: flex;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="任务单号:" prop="ycddrwRwdh" label-width="150" >
					<u-input placeholder=" " v-model="model.ycddrwRwdh" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="订单编号:" prop="ycddDdbh" label-width="150" >
					<u-input placeholder=" " v-model="model.carddYcdd.ycddDdbh" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
			</view> -->
			<u-form-item label="用车单位:" prop="ycddYcdw" label-width="150">
				<js-select v-model="model.ycddYcdw" :showFilter="true" :items="ycddYcdwSelectList" placeholder="请选择"
					:tree="true" :label-value="model.basCus.khdaDwqc"
					@label-input="model.basCus.khdaDwqc = $event"></js-select>
			</u-form-item>

			<view style="display: flex;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="联系人:" prop="ycddLxr" label-width="150">
					<u-input placeholder=" " v-model="model.ycddLxr" type="text" di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="联系电话:" prop="ycddLxdh" label-width="150">
					<u-input placeholder=" " v-model="model.ycddLxdh" type="text" di maxlength="64"></u-input>
				</u-form-item>
			</view>

			<view style="display: flex;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="部门:" prop="ycddBm" label-width="150">
					<u-input placeholder=" " v-model="model.ycddBm" type="text" di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="业务员:" prop="ycddYwy" label-width="150">
					<u-input placeholder=" " v-model="model.ycddYwy" type="text" :disabled="true" di
						maxlength="64"></u-input>
				</u-form-item>
			</view>



			<view style="display: flex;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="上车地点:" prop="ycddScdd" label-width="150">
					<u-input placeholder="请输入 " v-model="model.ycddScdd" type="text" di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="下车地点:" prop="ycddXcdd" label-width="150">
					<u-input placeholder="请输入 " v-model="model.ycddXcdd" type="text" di maxlength="64"></u-input>
				</u-form-item>
			</view>

			<u-form-item label="用车开始:" prop="ycddYckssj" label-width="170">
				<u-input placeholder="请选择" v-model="model.ycddYckssj" type="select" class="input-align"
					@click="ycddYckssjTime = true" />
			</u-form-item>
			<u-picker mode="time" :params="params" v-model="ycddYckssjTime" @confirm="ycddYckssjConfirm"></u-picker>
			<u-form-item label="用车结束:" prop="ycddYcjssj" label-width="170">
				<u-input placeholder="请选择" v-model="model.ycddYcjssj" type="select" class="input-align"
					@click="ycddYcjssjTime = true" />
			</u-form-item>
			<u-picker mode="time" :params="params" v-model="ycddYcjssjTime" @confirm="ycddYcjssjConfirm"></u-picker>

			<!-- <u-form-item  label="用车时间:" prop="datetime1" label-width="150" >
				<u-input placeholder=" " v-model="model.datetime1" type="text" :disabled="true" di maxlength="64"></u-input>
			</u-form-item> -->
			<view style="display: flex;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="租车时长:" prop="ycddZcsz" label-width="150">
					<u-input placeholder=" " v-model="ycddZcsz" type="text" :disabled="true" di
						maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="付款方式:" prop="ycddFkfs" label-width="150">
					<u-input placeholder="请输入" v-model="model.ycddFkfs" type="text" di maxlength="64"></u-input>
				</u-form-item>
			</view>



			<view style="display: flex;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="业务类型:" prop="coreYwlx" label-width="150">
					<js-select v-model="model['coreYwlx.id']" :showFilter="true" :items="ycddYcdwSelectList"
						placeholder="请选择" :tree="true" :label-value="model['coreYwlx.ywlxLxmc']"
						@label-input="model['coreYwlx.ywlxLxmc'] = $event"></js-select>
				</u-form-item>

				<u-form-item style="flex: 1;" label="订单押金:" prop="ycddDdyj" label-width="150">
					<u-input placeholder="请输入 " v-model="model.ycddDdyj" type="text" di maxlength="64"></u-input>
				</u-form-item>
			</view>

			<view style="display: flex;">

				<u-form-item style="flex: 1;" label="结算方式:" prop="ycddJsfs" label-width="150">
					<js-select v-model="model.ycddJsfs" dict-type="car_settle_tpye" placeholder="请选择"></js-select>
				</u-form-item>
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="专项名称:" prop="ycddZxmc" label-width="150">
					<js-select v-model="model.ycddZxmc" :showFilter="true" :items="ycddYcdwSelectList" placeholder="请选择"
						:tree="true" :label-value="model['coreYwlx.ywlxLxmc']"
						@label-input="model['coreYwlx.ywlxLxmc'] = $event"></js-select>
				</u-form-item>
			</view>


			<view style="display: flex;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="整单折扣:" prop="ycddZdzk" label-width="150">
					<u-input placeholder="请输入" v-model="ycddZdzk" type="text" di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="预收金额:" prop="ycddYsje" label-width="150">
					<u-input placeholder="请输入 " v-model="model.ycddYsje" type="text" di maxlength="64"></u-input>
				</u-form-item>
			</view>
			<view style="display: flex;">
				<u-form-item style="flex: 1;margin-right: 20rpx;" label="任务编号:" prop="ycddRwbh" label-width="150">
					<u-input placeholder="请输入 " v-model="ycddRwbh" type="text" di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="任务等级:" prop="ycddRwdj" label-width="150">
					<u-input placeholder="请输入 " v-model="model.ycddRwdj" type="text" di maxlength="64"></u-input>
				</u-form-item>
			</view>

			<u-form-item label="车辆要求:" prop="ycddClyq" label-width="150">
				<u-input placeholder="请输入 " v-model="model.ycddClyq" type="text" di maxlength="64"></u-input>
			</u-form-item>
			<u-form-item label="车辆行程:" prop="ycddClxc" label-width="150">
				<u-input placeholder="请输入 " v-model="model.ycddClxc" type="text" di maxlength="64"></u-input>
			</u-form-item>


		</u-form>
		<view class="action bg-white "
			style="font-size: 20px;align-items: center;display: flex;justify-content: space-between;padding: 0 10px 10px 10px;">

			<view style="align-items: center;display: flex;">
				<!-- <u-icon name="/static/image/detail.png" size="80"></u-icon> -->
				<text class="cuIcon-titles text-bold">用车信息</text>
			</view>


			<view @click="GoaddChild()">
				<u-icon name="plus-circle" size="90" color="#3E97B0"></u-icon>
			</view>
		</view>
		<view style="padding: 10px;">
			<u-empty v-if="!children.length" text="暂无用车信息" mode="data"></u-empty>
			<view v-for="(item,index) in children" class="cu-item shadow "
				style="position: relative;border: 1px solid #eee;border-radius: 10rpx;margin-bottom: 10rpx;"
				:key="index" v-if="item.status != '1'">
				<view class="cu-form-group" style="display: flex;justify-content: space-between;">
					<view
						style=" display: inline-block;padding: 5px;background: #3E97B0;color: #fff;min-width: 40px;text-align: center;font-style: italic;font-weight: bold">
						{{ index + 1 }}
					</view>
					<view class="">
						<u-icon name="trash" size="65" color="red" @click.stop="delDetail(item,index)"></u-icon>

					</view>
				</view>
				<view @click="edit(item,index)">
					<u-form-item style="flex: 1;" label="日期:" prop="btRq" label-width="230">
						{{item.btRq}}
						<!-- <u-input placeholder=" " v-model="item.btRq" :disabled="true" type="text"  /> -->
					</u-form-item>
					<u-form-item style="flex: 1;" label="行驶路线:" prop="btXslx" label-width="230">
						{{item.btXslx}}
						<!-- <u-input placeholder=" " v-model="item.btXslx" type="text" :disabled="true"  di maxlength="64"></u-input> -->
					</u-form-item>
					<!-- <u-form-item style="flex: 1;" label="起始路码:" prop="btQslm" label-width="230" >
					<u-input placeholder=" " v-model="item.btQslm" type="text" :disabled="true"  di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="起始路码照片:" prop="upload" label-width="230" >
					<js-uploadfile  v-model="item.dataMap" maxCount="0"  imageMaxWidth="-1" imageMaxHeight="-1" :biz-key="item.id" biz-type="carddYcddrw_file_bt_qs"></js-uploadfile>
				</u-form-item>
				<u-form-item style="flex: 1;" label="结束路码:" prop="btJslm" label-width="230" >
					<u-input placeholder=" " v-model="item.btJslm" type="text"  :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="结束路码照片:" prop="upload" label-width="230" >
					<js-uploadfile  v-model="item.dataMap"  maxCount="0" imageMaxWidth="-1" imageMaxHeight="-1" :biz-key="item.id" biz-type="carddYcddrw_file_bt_js"></js-uploadfile>
				</u-form-item>
				<u-form-item style="flex: 1;" label="实际公里:" prop="btSjgl" :disabled="true" label-width="230" >
					<u-input placeholder=" " :disabled="true" v-model="item.btSjgl" type="text"  di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="上车时间:" prop="btScsj" label-width="230" >
					<u-input placeholder=" " v-model="item.btScsj" type="text" :disabled="true"/>
				</u-form-item>
				<u-form-item style="flex: 1;" label="下车时间:" prop="btXcsj" label-width="230" >
					<u-input placeholder=" " v-model="item.btXcsj" type="text" :disabled="true"/>
				</u-form-item>
				<u-form-item style="flex: 1;" label="超时时间（小时):" prop="btCssjxs" label-width="230" >
					<u-input placeholder=" " v-model="item.btCssjxs" type="digit" :disabled="true" di maxlength="64"></u-input>
				</u-form-item>
				<u-form-item style="flex: 1;" label="备注:" prop="btBz" label-width="230" >
					<u-input placeholder=" " v-model="item.btBz" type="text" :disabled="true" di maxlength="64"></u-input>
				</u-form-item> -->
				</view>
			</view>
		</view>



		<view style="height: 250rpx;"></view>

		<view class="footer" style="padding: 20rpx 40rpx;">
			<view style=" display: flex;justify-content: space-between;margin-bottom: 20rpx; ">
				<u-button style="flex: 1;margin-right: 20rpx;" class="btn" type="primary"
					@click="handleSubmit('0', model.ycddrwRwzt, '')">暂存</u-button>
				<u-button v-if="model.showRwwcBtn" style="flex: 1;" class="btn" type="primary"
					@click="handleSubmit('1', model.ycddrwRwzt, '7')">任务完成</u-button>
			</view>
			<view style=" display: flex;justify-content: space-between; ">
				<u-button v-if="model.showQxwcBtn" style="flex: 1;margin-right: 20rpx;" class="btn" type="primary"
					@click="handleSubmit('1', model.ycddrwRwzt, '6')">取消完成</u-button>
				<u-button v-if="model.showTjrw2Btn" style="flex: 1;" class="btn" type="primary"
					@click="handleSubmit('1', model.ycddrwRwzt, '8')">提交任务</u-button>
			</view>
		</view>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				// value: 
				model: {
					// id:'',
					// carNo:'',
					// planDate:'',
					// cdriver:'',
					// driverPhone:'',
					// carVenCode:'',
					// carVenName:'',
					// remarks:'',
				},
				ycddYcdwSelectList: [],
				ycddYckssjTime: false,
				ycddYcjssjTime: false,
				params: {
					year: true,
					month: true,
					day: true,
					hour: true,
					minute: true,
					// second: true
				},
				children: [],
				pickerTime: false, //控制日期显示
			}
		},
		onLoad(params) {
			// this.model = JSON.parse(params.model)
			this.model = params
			this.$u.api.car.orderform({
				id: this.model.id
			}).then(res => {
				this.model = res.carddYcdd

				this.children = res.carddYcdd.carddYcddBtList
			});
			// uni.$on('detailsData', (data)=>{
			// 	console.log(data);
			// 	this.$u.api.car.carddYcddrwlform({id:this.model.id}).then(res => {
			// 		this.model = res.carddYcddrw
			// 		this.children = res.carddYcddrw.carddYcddrwBtList
			// 	});
			// });

		},
		onShow() {
			// this.children =  []

		},
		onReady() {
			this.$u.api.office.treeData().then(res => {
				this.ycddYcdwSelectList = res;
			});
		},
		methods: {
			ycddYckssjConfirm(e) {
				//+ " " + e.hour+ ":" + e.minute + ":" + e.second
				this.model.ycddYckssj = e.year + "-" + e.month + "-" + e.day + " " + e.hour + ":" + e.minute
			},
			ycddYcjssjConfirm(e) {
				//+ " " + e.hour+ ":" + e.minute + ":" + e.second
				this.model.ycddYcjssj = e.year + "-" + e.month + "-" + e.day + " " + e.hour + ":" + e.minute
			},
			fyhjInput(e) {
				console.log(e, '6789999');
			},
			delDetail(item, index) {
				let that = this
				uni.showModal({
					title: '删除提示',
					content: '是否删除此货位信息？',
					confirmColor: '#F54E40',
					success: (res) => {
						if (!res.confirm) {
							return false;
						}

						// that.children.splice(index, 1);
						// item.status = '1'
						this.$u.api.car.carddYcddrwldeleteBt({
							id: item.id
						}).then(res => {
							if (res.result == 'true') {
								this.$u.toast(res.message);
								this.children.splice(index, 1);
							} else {
								this.$refs.jsError.showError('', res.message, 'error');
							}
						})
						that.$forceUpdate()
					}
				})
			},
			handleSubmit(ope, oldStatus, newStatus) {
				let data = {
					...this.model,
					ope,
					oldStatus,
					newStatus
				}
				data.carddYcddBtList = this.children
				this.$u.api.car.carddYcddrwlsave(data).then(res => {
					if (res.result == 'true') {
						this.$u.toast(res.message);
						setTimeout(() => {
							uni.navigateBack({
								delta: 1
							})
						}, 500)
					} else {
						this.$refs.jsError.showError('', res.message, 'error');
					}
				})
			},
			edit(item, index) {
				// item.index = index
				// item.['parentId.id'] = this.model.id
				let obj = {
					...item,
					'parentId.id': this.model.id
				}
				uni.navigateTo({
					url: "/pages/index/detailsForm?item=" + JSON.stringify(obj),
				});
			},
			GoaddChild() {
				let obj = {
					parentId: {
						id: this.model.id,
					}
				}
				uni.navigateTo({
					url: "/pages/index/detailsForm?item=" + JSON.stringify(obj),
				});
			},
			renwu() {},
			chejian() {
				// uni.navigateTo({
				// 	url: "/pages/index/form2?id=" + this.model.id,
				// });
			},
			baocun() {},
			quxiao() {
				uni.navigateBack({
					delta: 1
				})
			},
			showKeyboard(ref) {
				this.$refs[ref].toShow(this.model.carNo)
			},
			timeConfirm(e) {
				this.model.planDate = e.year + '-' + e.month + '-' + e.day + ' ' + e.hour + ":" + e.minute + ":" + e
				.second;
			},
			submit(data, callback) {
				if (this.model.carNo == null || this.model.carNo == '') {
					this.$refs.jsError.showError('', '请正确填入车牌号！', 'error');
					return;
				} else if (this.model.planDate == null || this.model.planDate == '') {
					this.$refs.jsError.showError('', '请正确选择计划发货日期！', 'error');
					return;
				} else if (this.model.carVenCode == null || this.model.carVenCode == '') {
					this.$refs.jsError.showError('', '请正确选择运输单位！', 'error');
					return;
				} else if (this.model.ipicture == undefined || this.model.ipicture == '') {
					this.$refs.jsError.showError('', '请必填图片是否可再次上传！', 'error');
					return;
				} else {
					var data = {
						mfCarplanFhH: JSON.stringify(this.model),
						useStatus: 1
					};
					this.$u.api.mffh.save(data).then(res => {
						if (res.result == 'true') {
							this.$u.toast(res.message);
							setTimeout(() => {
								uni.$emit('refreshData');
								uni.navigateBack({
									delta: 1
								})
							}, 500)
						} else {
							this.$refs.jsError.showError('', res.message, 'error');
						}
					});
				}
			},
		}
	}
</script>
<style scoped lang="less">
	.footer {
		position: fixed;
		left: 0;
		bottom: 0;
		width: 100%;
		background: #fff;
		z-index: 999;
		border-top: 1px solid #aaa;
	}

	.cu-bar {
		min-height: 60px;
	}

	.cu-btn {
		font-size: 16px;
		height: 40px;
	}

	.foot {
		background: #fff;
	}

	.cu-item {
		flex-wrap: wrap;
		padding: 8px 15px !important;
	}

	.title {
		width: 220rpx;
	}

	.cu-modal-slot {
		margin: 42rpx 32rpx 20rpx 32rpx;
		height: 100vh;
		position: relative;
	}

	.cu-modal-header {
		font-size: 38rpx;
		color: #3d3d3d;
		line-height: 40rpx;
		text-align: left;
		font-weight: 500;
	}

	.button {
		font-size: 32rpx;
		color: #666666;
		line-height: 40rpx;
		padding: 12rpx 40rpx;
		margin-bottom: 20rpx;
		background: #e2e2e2;
		border-radius: 180rpx;
	}

	.button:hover {
		background: #3e97b0;
		color: #ffffff;
	}

	.cu-modal-search-title {
		margin: 32rpx 0rpx 20rpx 0rpx;
		text-align: left;
		font-size: 32rpx;
		color: #999999;
		line-height: 40rpx;
	}

	.cu-dialog {
		background: #ffffff;
	}

	.cu-modal-footer {
		padding: 32rpx 32rpx;
		width: 100%;

		.cu-btn {
			width: 50%;
		}
	}

	.bg-confirm {
		background: #3e97b0;
		color: #ffffff;
	}
</style>