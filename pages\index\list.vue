<template>
	<view >
		<!-- <js-lang title="home.title" :showBtn="true"></js-lang> -->
		<u-swiper :height="300" :list="imgList" :title="false" @click="imgListClick"></u-swiper>
		<!-- <view v-for="res in menuList1" :key="res.menuCode">
			<view class="cu-bar bg-white solid-bottom">
				<view class="action ">
					<u-icon name="/static/image/zfgs/index/icon@2x(6).png" size="80"></u-icon>
					<text class="text-lg  text-bold " style="font-size: 42rpx;">{{res.menuName}}</text>
				</view>
			</view>
			<view class="flex margin-sm flex-wrap justify-between u-skeleton">
				<view class="flex bg-white padding radius " v-for="item in res.childList"
					:key="item.menuName" @click="navTo(item.url)" style="margin-bottom: 15rpx;">
					<view class="xm-item u-skeleton-rect">
						<view class="xm-item-1">{{item.menuName}}</view>
						<view class="xm-item-2">{{item.menuTitle}}</view>
					</view>
					<image style="width: 86rpx; height: 86rpx" :src="item.menuIcon" mode="aspectFit"
						class="u-skeleton-circle" />
				</view>
			</view>
		</view>
		 -->
		 
		<!-- <view class="content" style="flex-direction: column;">
		 	<view style="width: 100%;background: #fff;border-radius: 10px;">
		 		<view style="padding: 10px 0 0 20px;font-weight: bold;">
		 			订单信息
		 		</view>
		 		<view class="Grid">
		 			<view class="Grid-Item" v-for="item in List1" :key="item.id" @click="jump(item.url)">
		 				<view class="GSimg">
		 					<image class="Image" :src="item.img"></image>
		 					<view class="GStitle">{{ item.title }}</view>
		 				</view>
		 
		 			</view>
		 		</view>
		 	</view>
		 </view> -->
		 
		 <view class="content" style="flex-direction: column;">
		 	<view style="width: 100%;background: #fff;border-radius: 10px;">
		 		<view style="padding: 10px 0 0 20px;font-weight: bold;">
		 			任务信息
		 		</view>
		 		<view class="Grid">
		 			<view class="Grid-Item" v-for="item in List2" :key="item.id" @click="jump(item.url)">
		 				<view class="GSimg" style="position: absolute;">
							<u-badge v-if="item.id==1" :count="count" :offset="[-10,0]"></u-badge>
		 					<image class="Image" :src="item.img"></image>
		 					<view class="GStitle">{{ item.title }}</view>
		 				</view>
		 
		 			</view>
		 		</view>
		 	</view>
		 </view>
		 
		 <view class="content" style="flex-direction: column;">
		 	<view style="width: 100%;background: #fff;border-radius: 10px;">
		 		<view style="padding: 10px 0 0 20px;font-weight: bold;">
		 			<!-- 车辆信息 -->
					其它信息
		 		</view>
		 		<view class="Grid">
		 			<view class="Grid-Item" v-for="item in List3" :key="item.id" @click="jump(item.url)">
		 				<view class="GSimg">
		 					<image class="Image" :src="item.img"></image>
		 					<view class="GStitle">{{ item.title }}</view>
		 				</view>
		 
		 			</view>
		 		</view>
		 	</view>
		 </view>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				count:0,
				List1: [{
						id: 999,
						img: '/static/jeesite/img/dd.png',
						url: '/pages/order/index',
						title: '订单维护'
					},
					
				],
				List2: [{
						id: 1,
						img: '/static/jeesite/img/rw.png',
						url: '/pages/index/index/index_rw',
						title: '订单任务'
					},
					// {
					// 	id: 2,
					// 	img: '/static/jeesite/img/db.png',
					// 	url: '/pages/index/index/index_db',
					// 	title: '待办任务'
					// },
					{
						id: 3,
						img: '/static/jeesite/img/yb.png',
						url: '/pages/index/index/index_yb',
						title: '已办任务'
					},
					{
						id: 4,
						img: '/static/jeesite/img/qb.png',
						url: '/pages/sys/index',
						title: '全部任务'
					},
				],
				List3: [{
						id: 1,
						img: '/static/jeesite/img/cl.png',
						url: '/pages/car/index',
						title: '车辆档案'
					},
					{
						id: 2,
						img: '/static/jeesite/img/wz.png',
						url: '/pages/wzjl/index',
						title: '车辆违章'
					},
					{
						id: 3,
						img: '/static/jeesite/img/gz.png',
						url: '/pages/jbgz/form',
						title: '薪资明细'
					},
					
				],
				stringPermissions: [],
				imgList: [
					{image: '/static/jeesite/banner/1.jpg'},
					{image: '/static/jeesite/banner/3.jpg'},
				],
				todoCount: 0,
				menuList1: [],
				menuList: [
					// {
					// 	isSHow:true,
					// 	menuCode: 'a10',
					// 	menuName: '发货装车计划',
					// 	menuIcon: '/static/common/img/dj.png',
					// 	menuColor: '',
					// 	authInfo:'mf:fh:mfCarplanFhH:fhdj',
					// 	url: '/pages/mf/fh/fhdj',
					// },
					// 发货
					{
						extend:{
							extendS2:'menue:zhuangche:fahuo'
						},
						menuIcon: '/static/image/zfgs/index/icon@2x(1).png',
						url: '/pages/mf/fh/fh',
					},
					// 到货
					{
						extend:{
							extendS2:'menue:zhuangche:daohuo'
						},
						menuIcon: '/static/image/zfgs/index/jcqr.png',
						url: '/pages/mf/dh/dh',
					},
					// 到货单装
					{
						extend:{
							extendS2:'menue:zhuangche:dhdz'
						},
						menuIcon: '/static/image/zfgs/index/clcx.png',
						url: '/pages/mf/dh/dzList',
					},
					// 到货混装
					{
						extend:{
							extendS2:'menue:zhuangche:dhhz'
						},
						menuIcon: '/static/image/zfgs/index/gcrz.png',
						url: '/pages/mf/dh/hzList',
					},
					// 发货单装
					{
						extend:{
							extendS2:'menue:zhuangche:fhdz'
						},
						menuIcon: '/static/image/zfgs/index/icon@2x(12).png',
						url: '/pages/mf/fh/dzList',
					},
					// 发货混装
					{
						extend:{
							extendS2:'menue:zhuangche:fhhz'
						},
						menuIcon: '/static/image/zfgs/index/tstbz.png',
						url: '/pages/mf/fh/hzList',
					},
					// 废旧物资
					{
						extend:{
							extendS2:'menue:zhuangche:fjwz'
						},
						menuIcon: '/static/image/zfgs/index/gb.png',
						url: '/pages/mf/fjwz/index',
					},
					// 采购入库
					{
						extend:{
							extendS2:'menue:rkzy:cgrk'
						},
						menuIcon: '/static/image/zfgs/index/bzrz.png',
						url: '/pages/mf/cgrk/list',
					},
					{
						extend:{
							extendS2:'menue:rkzy:llsq'
						},
						menuIcon: '/static/image/zfgs/index/bzrz.png',
						url: '/pages/mf/llsq/list',
					},

				],
			}
		},
		onShow() {
			this.$u.api.car.carddYcddrwListCount().then(res => {
				console.log(res);
				this.count = res.data
			});
		},
		created() {
			 
		},
		onLoad() {
			// this.upgrade();
			var _this = this;
			this.$u.api.menuTree().then(res => {
				if (res.length > 0) {
					res.forEach(item => {
						if ('移动端管理' == item.menuName) {
							item.childList.forEach(item2 => {
								if ('APP菜单' == item2.menuName) {
									this.showMenu(item2.childList);
								}
							})

						}
					})

				}
			});
			// this.$u.api.authInfo().then(res => {
			// 	this.stringPermissions=(res==null || res.stringPermissions==null)?[]:res.stringPermissions;
			// });
		},
		methods: {
			jump(url) {
				uni.navigateTo({
					url,
				})
			},
			
			//显示菜单
			showMenu(list) {
				this.menuList1 = list
				this.menuList.forEach(item => {
					this.menuList1.forEach(res => {
						res.childList.forEach(req => {
							if (req.extend?.extendS2 == item.extend?.extendS2 ) {
								req.menuIcon = item.menuIcon;
								req.url = item.url;
							}
						})

					})
				})

				console.log(this.menuList1, 'this.menuList1===');
			},
			navTo(url) {
				uni.navigateTo({
					url: url
				});
			},
			imgListClick(index) {
				console.log(`点击了第${index + 1}页图片`)
			},
			itemClick(index) {
				console.log(index);
			}
		}
	}
</script>

<style lang="scss">
	page {
		background-color: #f8f8f8;
	}
	.content {
		/* text-align: center; */
		padding: 10px;
		// background-color: #eee;
		height: 100%;
		display: flex;
		flex-direction: column;
	}

	.bottom {
		color: #000000;
	}

	.line {
		width: 1000px;
		height: 1.5px;
		background-color: #e5e5e5;
	}

	.wrap {
		padding: 24rpx;
	}

	.u-row {
		margin: 40rpx 0;
	}

	.demo-layout {
		height: 80rpx;
		border-radius: 8rpx;
	}

	.bg-purple {
		background: #d3dce6;
	}

	.bg-purple-light {
		background: #e5e9f2;
	}

	.bg-purple-dark {
		background: #99a9bf;
	}

	.Grid {
		width: 100%;
		float: left;
		overflow: hidden;
		display: flex;
		flex-wrap: wrap;
		// justify-content: space-between;
		align-content: space-between;
		// background: #f7f7f7;
		// padding: 10rpx 0;
		// background: url('/static/image/userBg.png');
		// background-size: cover;
		// background-position: bottom;

		.Grid-Item {
			width: 25%;
			height: 213rpx;
			text-align: center;
			// border:1rpx solid #ccc;
			box-sizing: border-box;

			.GSimg {
				width: 96rpx;
				height: 96rpx;
				margin-top: 42rpx;
				margin-left: 50rpx;

				.Image {
					width: 60rpx;
					height: 60rpx;
				}
			}

			.GStitle {
				width: 100%;
				height: 34rpx;
				line-height: 30rpx;
				color: #06121e;
				font-size: 24rpx;
				margin-top: 18rpx;
			}
		}
	}
</style>