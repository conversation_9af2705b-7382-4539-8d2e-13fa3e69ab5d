<template>
  <view class="wrap">
    <js-error mode="bottom" ref="jsError"></js-error>
    <view style="background-color: #fff;">
      <u-form
        class="form bg-white"
        :model="model"
        ref="uForm"
        label-position="left"
        style="padding: 0 10px"
      >
		<u-form-item
		  label="医院名称:"
		  prop="hospName"
		  label-width="200"
		  required
		  :label-style="{ 'font-weight': 'bold'}"
		>
		  <!-- <js-select v-model="model.binvBatch" dict-type="sys_yes_no" placeholder="请选择医院"></js-select> -->
			
			<!-- :disabled='model.id?true:false'  -->
			<js-select  :disabled='true' :childName="'None'" :showFilter="true" v-model="model.hospCode" :items="selectList"
				placeholder="请选择" :tree="false" :label-value="model.hospName" :flag="true"
				@label-input="model.hospName = $event"  @confirm="selectConfirm">
				<!-- @confirm="selectConfirm" -->
			</js-select>
		
		</u-form-item>
		
		<u-form-item
		  label="车牌号:"
		  prop="carNo"
		  label-width="200"
		  required
		  :label-style="{ 'font-weight': 'bold'}"
		>
		  <!-- <js-select v-model="model.binvBatch" dict-type="sys_yes_no" placeholder="请选择车牌号"></js-select> -->
		
		<!-- :disabled='model.id?true:false' -->
			<js-select   :childName="'None'" :showFilter="true" v-model="model.carNo" :items="selectCarList"
				placeholder="请选择" :tree="false" :label-value="model.carNo" :flag="true"
				@label-input="model.carNo = $event" >
				<!-- @confirm="selectConfirm" -->
			</js-select>
		
		</u-form-item>
		
		<u-form-item
		  label="驾驶员:"
		  prop="driver"
		  label-width="200"
		  required
		  :label-style="{ 'font-weight': 'bold'}"
		>
		  <!-- <js-select v-model="model.binvBatch" dict-type="sys_yes_no" placeholder="请选择车牌号"></js-select> -->
		
		<!-- :disabled='model.id?true:false' -->
			<js-select   :childName="'None'" :showFilter="true" v-model="model.driverName" :items="userSelectList"
				placeholder="请选择" :tree="false" :label-value="model.driverName"
				@label-input="model.driverName = $event" >
				<!-- @confirm="selectConfirm" -->
			</js-select>
		
		</u-form-item>
		
		
		<!-- <u-form-item label="驾驶员" prop="driver" required label-width="200" :label-style="{ 'font-weight': 'bold'}">
			<js-select :childName="'None'" :showFilter="true" v-model="model.driver" :items="userSelectList" placeholder="请选择" :tree="false"
				:label-value="model.driverName" @label-input="model.driverName = $event" :flag="true"></js-select>
		</u-form-item> -->
		
		<u-form-item label="押运员:"  prop="driver2" label-width="200" :label-style="{ 'font-weight': 'bold'}">
			<js-select :childName="'None'" clearable :showFilter="true" v-model="model.driver2" :items="userSelectList" placeholder="请选择" :tree="false"
				:label-value="model.driver2Name" @label-input="model.driver2Name = $event" ></js-select>
		</u-form-item>
		
		
		
		<u-form-item
		  label="感染数(箱):"
		  prop="qtyGr"
		  label-width="200"
		  :label-style="{ 'font-weight': 'bold'}"
		>
		<!-- required -->
		  <input v-model="model.qtyGr" type="number"  placeholder="请输入"
		  	@input="replaceInput($event,'1')" clearable />
		</u-form-item>
		<u-form-item
		  label="损伤数(箱):"
		  prop="qtyPs"
		  label-width="200"
		  :label-style="{ 'font-weight': 'bold'}"
		>
		<!-- required -->
		  <input v-model="model.qtyPs" type="number"   placeholder="请输入"
		  	@input="replaceInput($event,'2')" clearable />
		</u-form-item>
		<u-form-item
		  label="总数(箱):"
		  prop="qtySum"
		  label-width="200"
		  :label-style="{ 'font-weight': 'bold'}"
		>
		<!--   required -->
		  <input v-model="model.qtySum" type="number" disabled  placeholder=" "
		  	@input="replaceInput($event,'3')" clearable />
		</u-form-item>
		<u-form-item
		  label="感染性(kg):"
		  prop="weightGr"
		  label-width="200"
		  required
		  :label-style="{ 'font-weight': 'bold'}"
		>
		  <u-input v-model="model.weightGr" type="digit"  placeholder="请输入"
		  	@input="replaceInput($event,'4')" clearable />
		</u-form-item>
		<u-form-item
		  label="损伤性(kg):"
		  prop="weightPs"
		  label-width="200"
		  required
		  :label-style="{ 'font-weight': 'bold'}"
		>
		  <u-input v-model="model.weightPs" type="digit"  placeholder="请输入"
		  	@input="replaceInput($event,'5')" clearable />
		</u-form-item>
		<u-form-item
		  label="总量(kg):"
		  prop="weightSum"
		  label-width="200"
		  required
		  :label-style="{ 'font-weight': 'bold'}"
		>
		  <u-input v-model="model.weightSum" type="digit" disabled placeholder=" "
		  	@input="replaceInput($event,'6')" clearable />
		</u-form-item>
		<u-form-item
		  label="图片上传:"
		  required
		  label-width="200"
		  :label-style="{ 'font-weight': 'bold'}"
		>
		  <js-uploadfile v-model="model.dataMap" :biz-key="model.id" biz-type="yf_sh_file"></js-uploadfile>
		</u-form-item>

        <!-- <u-form-item label="启用批次:" prop="cbatch" label-width="200" :label-style="{ 'font-weight': 'bold' }">
					<js-select v-model="model.binvBatch" :disabled="true" dict-type="sys_yes_no" placeholder=""></js-select>
				</u-form-item>
				<u-form-item label="启用货位:" prop="cbatch" label-width="200" :label-style="{ 'font-weight': 'bold' }">
					<js-select v-model="model.bwhPos" :disabled="true" dict-type="sys_yes_no" placeholder=""></js-select>
				</u-form-item> -->
        
        <!-- <u-form-item
          label="入库数量:"
          prop="num"
          label-width="180"
          :label-style="{ 'font-weight': 'bold', color: 'red' }"
        >
		  <u-input v-model="model.num" type="digit"  placeholder="请输入入库数量"
		  	@input="replaceInput" clearable />
        </u-form-item> -->
      </u-form>
    </view>
	<view class="form-footer b-btns" style="height: 80px;">
		<view class="footer" >
			<u-button  class="btn" type="primary" @click="submit">确定收运</u-button>
		</view>
	</view>
  </view>
</template>

<script>
import util from '@/common/fire.js'
export default {
  data() {
    return {
      stockListHeight: 0,
      showflag: 0,
      model: {
        id: "",
		// qtyGr:0,
		// qtyPs:0,
		// weightGr:0,
		// weightPs:0,
      },
      xmflag: false,
      iqty: "",
      flag: false,
      focus: true,
	  selectList: [],
	  selectCarList: [],
	  userSelectList: [],
      x: 650, //x坐标
      y: 650, //y坐标
    };
  },
  onLoad() {
	  
  	let that = this
  	const eventChannel = this.getOpenerEventChannel();
  	eventChannel.on('acceptDataFrom', function(data) {
		console.log(data,'that.modelthat.modelthat.model');
		console.log(that.model);
  		that.model = data
		// that.model.qtyGr = 0
		// that.model.qtyPs = 0
		that.model.qtySum = 0
		// that.model.weightGr = 0
		// that.model.weightPs = 0
		that.model.weightSum = 0
		that.model.driverName = data.driverObj?data.driverObj.userName:''
		that.model.driver2Name = data.driver2Obj?data.driver2Obj.userName:''
		that.model =  {...that.model}
  	})
	
	var _self = this;
	uni.getSystemInfo({
	  success: (e) => {
	    // resu 可以获取当前屏幕的高度
	    _self.stockListHeight = e.windowHeight - uni.upx2px(160);
	  },
	  fail: (res) => {},
	});
  },
  watch: {},
  onShow() {
	  this.getLTreeData()
  },
  onReady() {},
  methods: {
	  getLTreeData() {
	  	this.$u.api.yysh.m8ViewHospTreeData({isShowCode: false,bused:1}).then(res => {
	  		this.selectList = res.map(item => {
				item.value = item.id
				return item
			})
	  	})
		
		
		this.$u.api.yysh.m8ViewCarTreeData({isShowCode: false}).then(res => {
			this.selectCarList = res.map(item => {
				item.value = item.id
				return item
			})
		})
		
		// 人员和机构数据
		// this.$u.api.office.treeData({isLoadUser: true}).then(res => {
		// 	this.userSelectList = res;
		// });
		this.$u.api.yysh.empUserTreeData({officeCode:'01002'}).then(res => {
			this.userSelectList =  res.map(item => {
				item.value = item.id
				return item
			})
			this.userSelectList.unshift({value:'',name:''})
		});
	  },
	  replaceInput(e,type) {
	  	var that = this
		if(type == '4' || type == '5'){
			e = e.match(/^\d*(\.?\d{0,2})/g)[0]
		}

	  	this.$nextTick(() => {
			if(type == '1'){
				that.model.qtyGr = e.target.value
				that.model.qtySum =  Number(that.model.qtyGr?that.model.qtyGr:0) + Number(that.model.qtyPs?that.model.qtyPs:0)
				// that.model.qtySum = util.toFixed2(that.model.qtySum)
				that.model = {
					...that.model
				}
			}
			if(type == '2'){
				that.model.qtyPs = e.target.value
				that.model.qtySum =  Number(that.model.qtyGr?that.model.qtyGr:0) + Number(that.model.qtyPs?that.model.qtyPs:0)
				// that.model.qtySum = util.toFixed2(that.model.qtySum)
				that.model = {
					...that.model
				}
			}
			if(type == '3'){
				that.model.qtySum = e.target.value
			}
			if(type == '4'){
				that.model.weightGr = e
				
				that.model.weightSum =  Number(that.model.weightGr?that.model.weightGr:0) + Number(that.model.weightPs?that.model.weightPs:0)
				that.model.weightSum = util.toFixed2(that.model.weightSum)
				that.model = {
					...that.model
				}
			}
			if(type == '5'){
				that.model.weightPs = e
				that.model.weightSum =  Number(that.model.weightGr?that.model.weightGr:0) + Number(that.model.weightPs?that.model.weightPs:0)
				that.model.weightSum = util.toFixed2(that.model.weightSum)
				that.model = {
					...that.model
				}
			}
			if(type == '6'){
				that.model.weightSum = e
			}
	  		
	  	})
	  },
	  
	 async selectConfirm(){
		  this.$u.api.yysh.getPreByHosp({hospCode:this.model.hospCode}).then((res) => {
		    if (res.result == "true") {
				console.log(res);
				this.model = res.data
				this.model.qtySum = 0
				this.model.weightSum = 0
				this.model.driverName = data.driverObj?data.driverObj.userName:''
				this.model.driver2Name = data.driver2Obj?data.driver2Obj.userName:''
		    } 
		  });
	  },
    async submit() {
		console.log(this.model);
      if (!this.model.hospName) {
        this.$u.toast('请先选择医院！');
        return;
      }
	  if (!this.model.carNo) {
	    this.$u.toast('请先选择车牌号！');
	    return;
	  }
	  
	  if (!this.model.driverName) {
	    this.$u.toast('请先选择驾驶员！');
	    return;
	  }
	  
	  
	  
	  
	  // if (!this.model.qtyGr && this.model.qtyGr !=0  ) {
	  //   this.$u.toast('请输入感染箱数！');
	  //   return;
	  // }
	  // if (!this.model.qtyPs && this.model.qtyPs !=0  ) {
	  //   this.$u.toast('请输入破损箱数！');
	  //   return;
	  // }
	  // if (!this.model.qtySum && this.model.qtySum !=0  ) {
	  //   this.$u.toast('请输入总箱数！');
	  //   return;
	  // }
	  if (!this.model.weightGr && this.model.weightGr !=0  ) {
	    this.$u.toast('请输入感染重量！');
	    return;
	  }
	  if (!this.model.weightPs && this.model.weightPs !=0  ) {
	    this.$u.toast('请输入破损重量！');
	    return;
	  }
	  if (this.model.weightSum <= 0 || this.model.weightSum == 'NaN') {
	    this.$u.toast('总重量不能小于等于零！');
	    return;
	  }
	  
	  
	  if (!this.model.dataMap || !this.model.dataMap.yf_sh_file) {
	    this.$u.toast("请上传图片！");
	    return;
	  }
	  
	  
      let data = {
        preId: this.model.id?this.model.id:'',
		isNewRecord: this.model.id?false:true,
		qtyGr:this.model.qtyGr?this.model.qtyGr:0,
		qtyPs:this.model.qtyPs?this.model.qtyPs:0,
		qtySum:this.model.qtySum?this.model.qtySum:0,
		weightGr:this.model.weightGr?this.model.weightGr:0,
		weightPs:this.model.weightPs?this.model.weightPs:0,
		weightSum:this.model.weightSum,
		hospCode:this.model.hospCode,
		hospName:this.model.hospName,
		dataMap:this.model.dataMap,
		carNo:this.model.carNo,
		driverName:this.model.driverName,
		driver2Name:this.model.driver2Name,
      };
      this.$u.api.yysh.yfShRecordSave(data).then((res) => {
        if (res.result == "true") {
          this.$u.toast(res.message);
          setTimeout(() => {
            uni.navigateBack({
              delta: 1,
            });
          }, 500);
        } else {
          uni.showModal({
          	title: "提示",
          	content: res.message,
          	showCancel: false,
          	success: (res) => {},
          });
          // this.$refs.jsError.showError("", res.message, "error");
        }
      });
    },

  },
};
</script>
<style lang="scss">
page {
		// background-color: #f8f8f8;
		// background-color: #e6e6e6;
		background-color: #fff;
	}
.footer {
	position: fixed;
	left: 0;
	padding: 0 10px;
	bottom: 0px;
	width: 100%;
	z-index: 999;
	
}
</style>